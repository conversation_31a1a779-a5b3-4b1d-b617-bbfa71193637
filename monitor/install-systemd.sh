#!/bin/bash

# systemd服务安装脚本
# 用于将监控脚本安装为系统服务

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INSTALL_DIR="/opt/monitor"
SYSTEMD_DIR="/etc/systemd/system"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 记录日志函数
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✓${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ✗${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root用户运行此脚本"
        exit 1
    fi
}

# 检查必要文件
check_files() {
    local required_files=(
        "monitor-services.sh"
        "log-cleanup.sh"
        "service-monitor.conf"
        "service-monitor.service"
        "log-cleanup.service"
        "log-cleanup.timer"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$SCRIPT_DIR/$file" ]; then
            log_error "缺少必要文件: $file"
            exit 1
        fi
    done
    log_success "所有必要文件检查通过"
}

# 创建安装目录
create_install_dir() {
    if [ ! -d "$INSTALL_DIR" ]; then
        mkdir -p "$INSTALL_DIR"
        log_success "创建安装目录: $INSTALL_DIR"
    else
        log_warning "安装目录已存在: $INSTALL_DIR"
    fi
}

# 复制文件
copy_files() {
    log "复制脚本文件到 $INSTALL_DIR..."
    
    # 复制脚本文件
    cp "$SCRIPT_DIR/monitor-services.sh" "$INSTALL_DIR/"
    cp "$SCRIPT_DIR/log-cleanup.sh" "$INSTALL_DIR/"
    cp "$SCRIPT_DIR/service-monitor.conf" "$INSTALL_DIR/"
    
    # 设置执行权限
    chmod +x "$INSTALL_DIR/monitor-services.sh"
    chmod +x "$INSTALL_DIR/log-cleanup.sh"
    chmod 644 "$INSTALL_DIR/service-monitor.conf"
    
    log_success "脚本文件复制完成"
}

# 安装systemd服务文件
install_systemd_files() {
    log "安装systemd服务文件..."
    
    # 复制服务文件
    cp "$SCRIPT_DIR/service-monitor.service" "$SYSTEMD_DIR/"
    cp "$SCRIPT_DIR/log-cleanup.service" "$SYSTEMD_DIR/"
    cp "$SCRIPT_DIR/log-cleanup.timer" "$SYSTEMD_DIR/"
    
    # 设置权限
    chmod 644 "$SYSTEMD_DIR/service-monitor.service"
    chmod 644 "$SYSTEMD_DIR/log-cleanup.service"
    chmod 644 "$SYSTEMD_DIR/log-cleanup.timer"
    
    log_success "systemd服务文件安装完成"
}

# 重新加载systemd
reload_systemd() {
    log "重新加载systemd配置..."
    systemctl daemon-reload
    log_success "systemd配置重新加载完成"
}

# 启用并启动服务
enable_services() {
    log "启用并启动服务..."
    
    # 启用并启动监控服务
    systemctl enable service-monitor.service
    systemctl start service-monitor.service
    
    # 启用并启动定时器
    systemctl enable log-cleanup.timer
    systemctl start log-cleanup.timer
    
    log_success "服务启用和启动完成"
}

# 检查服务状态
check_status() {
    log "检查服务状态..."
    echo ""
    
    echo "=== 服务监控状态 ==="
    systemctl status service-monitor.service --no-pager -l
    echo ""
    
    echo "=== 日志清理定时器状态 ==="
    systemctl status log-cleanup.timer --no-pager -l
    echo ""
    
    echo "=== 定时器列表 ==="
    systemctl list-timers log-cleanup.timer --no-pager
}

# 卸载服务
uninstall_services() {
    log "开始卸载服务..."
    
    # 停止并禁用服务
    systemctl stop service-monitor.service 2>/dev/null || true
    systemctl disable service-monitor.service 2>/dev/null || true
    
    systemctl stop log-cleanup.timer 2>/dev/null || true
    systemctl disable log-cleanup.timer 2>/dev/null || true
    
    # 删除服务文件
    rm -f "$SYSTEMD_DIR/service-monitor.service"
    rm -f "$SYSTEMD_DIR/log-cleanup.service"
    rm -f "$SYSTEMD_DIR/log-cleanup.timer"
    
    # 重新加载systemd
    systemctl daemon-reload
    
    log_success "服务卸载完成"
    
    # 询问是否删除安装目录
    read -p "是否删除安装目录 $INSTALL_DIR? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -rf "$INSTALL_DIR"
        log_success "安装目录已删除"
    fi
}

# 显示帮助信息
show_help() {
    echo "systemd服务安装脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  install     安装systemd服务"
    echo "  uninstall   卸载systemd服务"
    echo "  status      查看服务状态"
    echo "  restart     重启服务"
    echo "  logs        查看服务日志"
    echo "  help        显示此帮助信息"
    echo ""
    echo "服务说明:"
    echo "  service-monitor.service  - 服务监控和自动重启"
    echo "  log-cleanup.timer       - 定时日志清理（每周日凌晨2点）"
    echo ""
    echo "安装位置:"
    echo "  脚本目录: $INSTALL_DIR"
    echo "  服务文件: $SYSTEMD_DIR"
}

# 重启服务
restart_services() {
    log "重启服务..."
    systemctl restart service-monitor.service
    systemctl restart log-cleanup.timer
    log_success "服务重启完成"
}

# 查看日志
show_logs() {
    echo "=== 服务监控日志 ==="
    journalctl -u service-monitor.service -f --no-pager -n 50
}

# 主程序
main() {
    case "$1" in
        "install")
            check_root
            check_files
            create_install_dir
            copy_files
            install_systemd_files
            reload_systemd
            enable_services
            check_status
            log_success "安装完成！"
            ;;
        "uninstall")
            check_root
            uninstall_services
            ;;
        "status")
            check_status
            ;;
        "restart")
            check_root
            restart_services
            ;;
        "logs")
            show_logs
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        *)
            echo "错误: 未知选项 '$1'"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主程序
main "$@"
