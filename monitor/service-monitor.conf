#!/bin/bash

# 服务监控配置文件

# 检查间隔（秒）
CHECK_INTERVAL=60

# 钉钉通知配置
# DING_API_URL="https://oapi.dingtalk.com/robot/send?access_token=98d48ef475e774b9f3181a3793e8339e421ae514d84e19e3deb677fb6de25c86"
DING_API_URL="https://oapi.dingtalk.com/robot/send?access_token=fc714da19e69cf75d6efe88686ee795e47b1909f9e188cca2c8578fffae95e0a"
# 注意：现在使用钉钉机器人webhook，不再需要用户列表

# 通知命令（可选）- 例如使用 curl 发送到钉钉或企业微信
# NOTIFICATION_CMD="curl -s -H 'Content-Type: application/json' -d '{\"text\":\"%2\"}' https://your-webhook-url"

# 要监控的服务列表（空格分隔）
SERVICES="GATEWAY AUTH SYSTEM CUSTOMER ERP THIRD JOB XXL_JOB"

# Gateway 服务配置
GATEWAY_NAME="nnb-gateway 服务"
GATEWAY_JAR_PATH="/home/<USER>/data/crm/java/nnb-gateway.jar"
# GATEWAY_JAVA_OPTS="-Xms512m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m"
GATEWAY_PROCESS_KEYWORD="nnb-gateway.jar"

# Auth 服务配置
AUTH_NAME="nnb-auth 服务"
AUTH_JAR_PATH="/home/<USER>/data/crm/java/nnb-auth.jar"
# AUTH_JAVA_OPTS="-Xms512m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m"
AUTH_PROCESS_KEYWORD="nnb-auth.jar"

# System 服务配置
SYSTEM_NAME="nnb-modules-system 服务"
SYSTEM_JAR_PATH="/home/<USER>/data/crm/java/nnb-modules-system.jar"
# SYSTEM_JAVA_OPTS="-Xms512m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m"
SYSTEM_PROCESS_KEYWORD="nnb-modules-system.jar"

# CUSTOMER 服务配置
CUSTOMER_NAME="nnb-modules-customer 服务"
CUSTOMER_JAR_PATH="/home/<USER>/data/crm/java/nnb-modules-customer.jar"
# CUSTOMER_JAVA_OPTS="-Xms512m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m"
CUSTOMER_PROCESS_KEYWORD="nnb-modules-customer.jar"

# ERP 服务配置
ERP_NAME="nnb-modules-erp 服务"
ERP_JAR_PATH="/home/<USER>/data/crm/java/nnb-modules-erp.jar.jar"
# ERP_JAVA_OPTS="-Xms512m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m"
ERP_PROCESS_KEYWORD="nnb-modules-erp.jar"

# THIRD 服务配置
THIRD_NAME="nnb-modules-third 服务"
THIRD_JAR_PATH="/home/<USER>/data/crm/java/nnb-modules-third.jar"
# THIRD_JAVA_OPTS="-Xms512m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m"
THIRD_PROCESS_KEYWORD="nnb-modules-third.jar"

# JOB 服务配置
JOB_NAME="nnb-modules-job 服务"
JOB_JAR_PATH="/home/<USER>/data/crm/java/nnb-modules-job.jar"
# JOB_JAVA_OPTS="-Xms512m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m"
JOB_PROCESS_KEYWORD="nnb-modules-job.jar"

# XXL_JOB 服务配置
XXL_JOB_NAME="xxl-job 服务"
XXL_JOB_JAR_PATH="/home/<USER>/data/xxl-job/xxl-job-admin-2.4.0.jar"
# XXL_JOB_JAVA_OPTS="-Xms512m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m"
XXL_JOB_PROCESS_KEYWORD="xxl-job-admin-2.4.0.jar"
