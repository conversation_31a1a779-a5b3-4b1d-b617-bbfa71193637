#!/bin/bash

# 服务监控和自动重启脚本
# 使用方法: ./monitor-services.sh [配置文件路径]

# 默认配置文件路径
CONFIG_FILE="${1:-./service-monitor.conf}"
LOG_FILE="/opt/monitor/logs/service-monitor.log"
LOG_CLEANUP_FLAG="/opt/monitor/logs/.log_cleanup_flag"

# 确保日志目录存在（使用当前用户权限创建）
ensure_log_directory() {
    if [ ! -d "/opt/monitor/logs" ]; then
        echo "创建日志目录: /opt/monitor/logs"
        mkdir -p "/opt/monitor/logs" 2>/dev/null || {
            echo "警告: 无法创建日志目录 /opt/monitor/logs"
            echo "请确保有足够权限或手动创建目录"
        }
    fi
}

# 调用目录创建函数
ensure_log_directory

# 钉钉通知配置
DING_API_URL="https://oapi.dingtalk.com/robot/send?access_token=98d48ef475e774b9f3181a3793e8339e421ae514d84e19e3deb677fb6de25c86"

# 确保日志文件存在
touch "$LOG_FILE"

# 记录日志函数
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# 发送钉钉通知函数
send_ding_notification() {
    local content="$1"

    # 构建钉钉机器人消息体
    local message_body=$(cat <<EOF
{
    "msgtype": "markdown",
    "markdown": {
        "title": "服务监控提醒",
        "text": "### 服务监控提醒 \n  * $content"
    }
}
EOF
)

    # 发送POST请求到钉钉机器人
    local response=$(curl -s -X POST "$DING_API_URL" \
        -H "Content-Type: application/json" \
        -d "$message_body" \
        --connect-timeout 10 \
        --max-time 30)

    if [ $? -eq 0 ]; then
        log "钉钉通知发送成功: $content"
    else
        log "钉钉通知发送失败: $content"
    fi
}

# 日志清理函数
cleanup_logs() {
    local current_time=$(date +%s)
    local cleanup_flag_time=0

    # 检查上次清理时间
    if [ -f "$LOG_CLEANUP_FLAG" ]; then
        cleanup_flag_time=$(cat "$LOG_CLEANUP_FLAG" 2>/dev/null || echo 0)
    fi

    # 计算时间差（秒）
    local time_diff=$((current_time - cleanup_flag_time))
    local seven_days_seconds=$((7 * 24 * 3600))

    # 如果距离上次清理超过7天，则执行清理
    if [ $time_diff -gt $seven_days_seconds ]; then
        log "开始执行service-monitor.log日志清理任务..."

        local cleaned_files=0

        # 固定日志文件位置
        local monitor_log_paths=(
            "/opt/monitor/logs/service-monitor.log"
        )

        # 清理service-monitor.log文件（7天前的）
        for log_path in "${monitor_log_paths[@]}"; do
            if [ -f "$log_path" ]; then
                local file_age=$(find "$(dirname "$log_path")" -name "$(basename "$log_path")" -type f -mtime +7 2>/dev/null | wc -l)
                if [ $file_age -gt 0 ]; then
                    log "清理service-monitor.log: $log_path"
                    rm -f "$log_path" 2>/dev/null
                    if [ $? -eq 0 ]; then
                        cleaned_files=$((cleaned_files + 1))
                    fi
                fi
            fi
        done

        # 更新清理标记文件
        echo "$current_time" > "$LOG_CLEANUP_FLAG"

        log "service-monitor.log日志清理任务完成，清理文件数量: $cleaned_files"
        send_ding_notification "service-monitor.log日志清理完成，已清理7天前的监控日志文件，清理数量: ${cleaned_files}个"
    fi
}

# 检查配置文件是否存在
if [ ! -f "$CONFIG_FILE" ]; then
    log "错误: 配置文件 $CONFIG_FILE 不存在"
    exit 1
fi

# 加载配置文件
source "$CONFIG_FILE"

# 检查必要的变量是否已定义
if [ -z "$SERVICES" ]; then
    log "错误: 配置文件中未定义服务列表 (SERVICES)"
    exit 1
fi

# 监控并重启服务的函数
monitor_and_restart() {
    local service_name="$1"
    local jar_path="$2"
    local java_opts="$3"
    local process_keyword="$4"

    # 检查服务是否运行
    if pgrep -f "$process_keyword" > /dev/null; then
        log "服务 $service_name 正在运行"
    else
        log "警告: 服务 $service_name 未运行，正在重启..."

        # 发送服务停止通知
        send_ding_notification "【服务告警】服务 $service_name 已停止运行，正在尝试自动重启..."

        # 获取jar文件所在目录
        local jar_dir=$(dirname "$jar_path")

        # 切换到jar目录
        cd "$jar_dir" || {
            log "错误: 无法切换到目录 $jar_dir"
            send_ding_notification "【服务告警】服务 $service_name 重启失败：无法切换到目录 $jar_dir"
            return 1
        }

        # 启动服务
        if [ -n "$java_opts" ]; then
            nohup java $java_opts -jar "$jar_path" --spring.profiles.active=prod >/dev/null 2>&1 &
        else
            # nohup java -jar "$jar_path" --spring.profiles.active=test > "$jar_dir/nohup.out" 2>&1 &
            nohup java -jar "$jar_path" --spring.profiles.active=prod >/dev/null 2>&1 &
            # log "执行命令: nohup java -jar $jar_path --spring.profiles.active=prod >/dev/null 2>&1 &"
        fi

        # 检查启动是否成功
        sleep 15
        if pgrep -f "$process_keyword" > /dev/null; then
            log "服务 $service_name 已成功重启"
            send_ding_notification "【服务恢复】服务 $service_name 已成功自动重启并正常运行"
        else
            log "错误: 服务 $service_name 重启失败"
            send_ding_notification "【服务告警】服务 $service_name 重启失败，请立即手动检查！"
        fi
    fi
}

# 脚本启动时的初始化
initialize_script() {
    log "服务监控脚本已启动"

    # 发送启动通知
    send_ding_notification "【系统通知】服务监控脚本已启动，开始监控服务状态"

    # 执行一次日志清理检查
    cleanup_logs
}

# 脚本退出时的清理函数
cleanup_on_exit() {
    log "服务监控脚本正在退出..."
    send_ding_notification "【系统通知】服务监控脚本已停止运行"
    exit 0
}

# 设置信号处理
trap cleanup_on_exit SIGINT SIGTERM

# 主循环
initialize_script

# 循环计数器，用于定期执行日志清理
loop_count=0

while true; do
    # 每100次循环检查一次日志清理（约每小时检查一次，假设CHECK_INTERVAL为60秒）
    if [ $((loop_count % 100)) -eq 0 ]; then
        cleanup_logs
    fi

    for service in $SERVICES; do
        # 获取服务配置
        service_name="${service}_NAME"
        jar_path="${service}_JAR_PATH"
        java_opts="${service}_JAVA_OPTS"
        process_keyword="${service}_PROCESS_KEYWORD"

        # 检查配置是否存在
        if [ -z "${!service_name}" ] || [ -z "${!jar_path}" ] || [ -z "${!process_keyword}" ]; then
            log "错误: 服务 $service 配置不完整"
            continue
        fi

        # 监控并在需要时重启服务
        monitor_and_restart "${!service_name}" "${!jar_path}" "${!java_opts}" "${!process_keyword}"
    done

    # 增加循环计数器
    loop_count=$((loop_count + 1))

    # 等待下一次检查
    sleep "$CHECK_INTERVAL"
done
