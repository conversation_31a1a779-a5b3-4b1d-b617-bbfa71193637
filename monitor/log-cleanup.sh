#!/bin/bash

# 日志清理脚本
# 使用方法: ./log-cleanup.sh [天数] [是否发送通知]
# 示例: ./log-cleanup.sh 7 true

# 默认配置
DAYS_TO_KEEP="${1:-7}"
SEND_NOTIFICATION="${2:-true}"
LOG_CLEANUP_FLAG="/var/log/.log_cleanup_flag"

# 钉钉通知配置
DING_API_URL="https://oapi.dingtalk.com/robot/send?access_token=98d48ef475e774b9f3181a3793e8339e421ae514d84e19e3deb677fb6de25c86"

# 记录日志函数
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" >> "/var/log/log-cleanup.log"
}

# 发送钉钉通知函数
send_ding_notification() {
    local content="$1"

    if [ "$SEND_NOTIFICATION" != "true" ]; then
        return 0
    fi

    # 构建钉钉机器人消息体
    local message_body=$(cat <<EOF
{
    "msgtype": "markdown",
    "markdown": {
        "title": "服务监控提醒",
        "text": "### 服务监控提醒 \n  * $content"
    }
}
EOF
)

    # 发送POST请求到钉钉机器人
    local response=$(curl -s -X POST "$DING_API_URL" \
        -H "Content-Type: application/json" \
        -d "$message_body" \
        --connect-timeout 10 \
        --max-time 30)

    if [ $? -eq 0 ]; then
        log "钉钉通知发送成功: $content"
    else
        log "钉钉通知发送失败: $content"
    fi
}

# 主要的日志清理函数
cleanup_logs() {
    log "开始执行service-monitor.log日志清理任务，保留 $DAYS_TO_KEEP 天内的日志..."

    local cleaned_files=0
    local total_size_before=0
    local total_size_after=0

    # 定义service-monitor.log的可能位置
    local monitor_log_paths=(
        "/var/log/service-monitor.log"
        "./service-monitor.log"
        "../service-monitor.log"
        "/tmp/service-monitor.log"
    )

    # 计算清理前的总大小
    for log_path in "${monitor_log_paths[@]}"; do
        if [ -f "$log_path" ]; then
            local file_size=$(du -b "$log_path" 2>/dev/null | cut -f1)
            total_size_before=$((total_size_before + file_size))
        fi
    done

    # 清理service-monitor.log文件
    log "清理service-monitor.log日志文件..."
    for log_path in "${monitor_log_paths[@]}"; do
        if [ -f "$log_path" ]; then
            local file_age=$(find "$(dirname "$log_path")" -name "$(basename "$log_path")" -type f -mtime +$DAYS_TO_KEEP 2>/dev/null | wc -l)
            if [ $file_age -gt 0 ]; then
                log "清理文件: $log_path"
                rm -f "$log_path" 2>/dev/null
                if [ $? -eq 0 ]; then
                    cleaned_files=$((cleaned_files + 1))
                    log "成功清理: $log_path"
                else
                    log "清理失败: $log_path"
                fi
            else
                log "文件未达到清理条件: $log_path (未超过${DAYS_TO_KEEP}天)"
            fi
        fi
    done

    # 计算清理后的总大小
    for log_path in "${monitor_log_paths[@]}"; do
        if [ -f "$log_path" ]; then
            local file_size=$(du -b "$log_path" 2>/dev/null | cut -f1)
            total_size_after=$((total_size_after + file_size))
        fi
    done

    # 计算释放的空间
    local freed_space=$((total_size_before - total_size_after))
    local freed_kb=$((freed_space / 1024))
    local freed_mb=$((freed_space / 1024 / 1024))

    # 更新清理标记文件
    echo "$(date +%s)" > "$LOG_CLEANUP_FLAG"

    log "service-monitor.log日志清理任务完成"
    log "清理文件数量: $cleaned_files"
    if [ $freed_mb -gt 0 ]; then
        log "释放磁盘空间: ${freed_mb}MB"
    else
        log "释放磁盘空间: ${freed_kb}KB"
    fi

    # 发送通知
    local notification_content="【系统维护】service-monitor.log日志清理任务完成
- 清理规则: 删除${DAYS_TO_KEEP}天前的service-monitor.log文件
- 清理文件数量: ${cleaned_files}个
- 释放磁盘空间: ${freed_kb}KB
- 清理时间: $(date +'%Y-%m-%d %H:%M:%S')"

    send_ding_notification "$notification_content"
}

# 检查是否需要清理（基于时间间隔）
check_and_cleanup() {
    local current_time=$(date +%s)
    local cleanup_flag_time=0
    local force_cleanup="${3:-false}"

    # 检查上次清理时间
    if [ -f "$LOG_CLEANUP_FLAG" ]; then
        cleanup_flag_time=$(cat "$LOG_CLEANUP_FLAG" 2>/dev/null || echo 0)
    fi

    # 计算时间差（秒）
    local time_diff=$((current_time - cleanup_flag_time))
    local seven_days_seconds=$((7 * 24 * 3600))

    # 如果距离上次清理超过7天，或者强制清理，则执行清理
    if [ $time_diff -gt $seven_days_seconds ] || [ "$force_cleanup" = "true" ]; then
        cleanup_logs
    else
        local remaining_days=$(((seven_days_seconds - time_diff) / 86400))
        log "距离下次自动清理还有 $remaining_days 天"
    fi
}

# 主程序
main() {
    log "日志清理脚本启动"

    # 创建日志目录（如果不存在）
    mkdir -p /var/log

    # 检查参数
    if [ "$1" = "--force" ]; then
        log "强制执行日志清理"
        cleanup_logs
    elif [ "$1" = "--check" ]; then
        log "检查是否需要清理"
        check_and_cleanup "$DAYS_TO_KEEP" "$SEND_NOTIFICATION" "false"
    else
        log "执行日志清理（保留 $DAYS_TO_KEEP 天）"
        cleanup_logs
    fi

    log "日志清理脚本结束"
}

# 执行主程序
main "$@"
