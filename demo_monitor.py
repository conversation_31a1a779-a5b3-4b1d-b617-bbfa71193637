#!/usr/bin/env python3
"""
监控脚本演示
演示如何使用Python版本的服务监控脚本
"""

import sys
import time
import importlib.util

# 动态导入monitor-services.py
spec = importlib.util.spec_from_file_location("monitor_services", "monitor-services.py")
monitor_services = importlib.util.module_from_spec(spec)
spec.loader.exec_module(monitor_services)

def create_demo_config():
    """创建演示配置文件"""
    config_content = """# 演示配置文件
CHECK_INTERVAL=10
DING_API_URL=
SERVICES=DEMO

# 演示服务配置
DEMO_NAME=演示Java服务
DEMO_JAR_PATH=/tmp/demo-service.jar
DEMO_JAVA_OPTS=-Xms256m -Xmx512m
DEMO_PROCESS_KEYWORD=demo-service.jar
"""
    
    with open('demo-config.conf', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✓ 创建演示配置文件: demo-config.conf")

def demo_monitoring():
    """演示监控功能"""
    print("=== Python版本Java服务监控演示 ===\n")
    
    # 创建演示配置
    create_demo_config()
    
    # 初始化监控器
    print("1. 初始化监控器...")
    monitor = monitor_services.ServiceMonitor('demo-config.conf')
    print(f"   ✓ 监控服务: {', '.join(monitor.services)}")
    print(f"   ✓ 检查间隔: {monitor.check_interval}秒")
    print(f"   ✓ 日志文件: {monitor.log_file}")
    
    # 获取服务配置
    print("\n2. 获取服务配置...")
    service_name, jar_path, java_opts, process_keyword = monitor.get_service_config('DEMO')
    print(f"   ✓ 服务名称: {service_name}")
    print(f"   ✓ JAR路径: {jar_path}")
    print(f"   ✓ Java选项: {java_opts}")
    print(f"   ✓ 进程关键字: {process_keyword}")
    
    # 检查服务状态
    print("\n3. 检查服务状态...")
    is_running = monitor.is_service_running(process_keyword)
    print(f"   ✓ 服务运行状态: {'运行中' if is_running else '未运行'}")
    
    # 演示监控循环（只运行几次）
    print("\n4. 演示监控循环...")
    print("   (模拟监控3次，每次间隔3秒)")
    
    for i in range(3):
        print(f"\n   第{i+1}次检查:")
        
        # 检查服务状态
        is_running = monitor.is_service_running(process_keyword)
        if is_running:
            print(f"   ✓ 服务 {service_name} 正在运行")
        else:
            print(f"   ⚠ 服务 {service_name} 未运行")
            print(f"   ℹ 在实际环境中，这里会自动重启服务")
        
        if i < 2:  # 不在最后一次等待
            print("   等待3秒...")
            time.sleep(3)
    
    print("\n5. 演示完成!")
    print("\n=== 功能特性总结 ===")
    print("✓ 配置文件解析 - 兼容bash格式")
    print("✓ 进程状态检测 - 使用psutil库")
    print("✓ 自动重启机制 - 检测到停止时重启")
    print("✓ 钉钉通知功能 - 服务状态变化通知")
    print("✓ 日志记录管理 - 自动记录和清理")
    print("✓ 信号处理机制 - 优雅退出")
    print("✓ 异常处理机制 - 健壮的错误处理")
    
    print("\n=== 使用方法 ===")
    print("1. 安装依赖: pip install requests psutil")
    print("2. 配置服务: 编辑 service-monitor-python.conf")
    print("3. 运行监控: python monitor-services.py")
    print("4. 后台运行: nohup python monitor-services.py &")
    
    print("\n=== 与bash版本对比 ===")
    print("优势:")
    print("  • 更好的错误处理和异常管理")
    print("  • 跨平台支持（Linux/macOS/Windows）")
    print("  • 更清晰的代码结构和可维护性")
    print("  • 更精确的进程检测（使用psutil）")
    print("  • 更好的依赖管理（pip）")
    print("\n兼容性:")
    print("  • 配置文件格式完全兼容")
    print("  • 功能行为完全一致")
    print("  • 钉钉通知格式相同")

def main():
    """主函数"""
    try:
        demo_monitoring()
    except KeyboardInterrupt:
        print("\n\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中发生错误: {e}")

if __name__ == "__main__":
    main()
