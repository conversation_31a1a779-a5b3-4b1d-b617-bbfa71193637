[Unit]
Description=Java Services Monitor
Documentation=https://github.com/your-repo/monitor-services
After=network.target network-online.target
Wants=network-online.target

[Service]
Type=simple
User=monitor
Group=monitor
WorkingDirectory=/opt/monitor
ExecStart=/usr/bin/python3 /opt/monitor/monitor-services.py /opt/monitor/monitor.conf
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# 环境变量
Environment=PYTHONPATH=/opt/monitor
Environment=PYTHONUNBUFFERED=1

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=monitor-services

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/monitor

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
