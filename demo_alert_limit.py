#!/usr/bin/env python3
"""
告警限制功能演示
展示如何使用告警限制功能避免告警轰炸
"""

import sys
import time
import importlib.util

# 动态导入monitor-services.py
spec = importlib.util.spec_from_file_location("monitor_services", "monitor-services.py")
monitor_services = importlib.util.module_from_spec(spec)
spec.loader.exec_module(monitor_services)

def demo_alert_limit():
    """演示告警限制功能"""
    print("=== 告警限制功能演示 ===\n")
    
    print("📋 功能说明:")
    print("• 每个服务最多连续告警5次（可配置）")
    print("• 超过限制后自动静默，避免告警轰炸")
    print("• 服务恢复时自动重置告警计数")
    print("• 多服务独立计数，互不影响")
    print()
    
    # 创建演示配置
    config_content = """# 告警限制演示配置
CHECK_INTERVAL=10
MAX_ALERT_COUNT=3
DING_API_URL=
SERVICES=DEMO_SERVICE

# 演示服务配置
DEMO_SERVICE_NAME=演示告警限制服务
DEMO_SERVICE_JAR_PATH=/tmp/demo-alert.jar
DEMO_SERVICE_JAVA_OPTS=
DEMO_SERVICE_PROCESS_KEYWORD=demo-alert-service
"""
    
    with open('demo-alert-limit.conf', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✓ 创建演示配置文件")
    
    # 初始化监控器
    monitor = monitor_services.ServiceMonitor('demo-alert-limit.conf')
    service_name = "演示告警限制服务"
    
    print(f"✓ 初始化监控器，最大告警次数: {monitor.max_alert_count}")
    print()
    
    # 演示场景1：连续告警直到静默
    print("🎬 场景1: 连续告警直到静默")
    print("-" * 40)
    
    for i in range(5):
        print(f"\n第{i+1}次告警:")
        
        # 获取当前状态
        current_count = monitor.alert_counters.get(service_name, 0)
        is_silenced = monitor.is_alert_silenced(service_name)
        
        print(f"  当前告警计数: {current_count}")
        print(f"  是否已静默: {is_silenced}")
        
        # 发送告警
        if not is_silenced:
            alert_sent = monitor.send_alert_with_limit(
                service_name, 
                f"【演示告警】服务故障第{i+1}次"
            )
            print(f"  告警发送: {'✓ 成功' if alert_sent else '✗ 已静默'}")
        else:
            print(f"  告警发送: ✗ 跳过（已静默）")
        
        # 显示最新状态
        final_count = monitor.alert_counters.get(service_name, 0)
        final_silenced = monitor.is_alert_silenced(service_name)
        print(f"  更新后计数: {final_count}")
        print(f"  更新后状态: {'已静默' if final_silenced else '正常'}")
        
        time.sleep(1)
    
    print()
    
    # 演示场景2：服务恢复，重置计数
    print("🎬 场景2: 服务恢复，重置告警计数")
    print("-" * 40)
    
    print("\n模拟服务恢复...")
    monitor.reset_alert_counter(service_name)
    
    count = monitor.alert_counters.get(service_name, 0)
    silenced = monitor.is_alert_silenced(service_name)
    print(f"✓ 告警计数已重置: {count}")
    print(f"✓ 静默状态已清除: {silenced}")
    
    print("\n重置后发送新告警...")
    alert_sent = monitor.send_alert_with_limit(
        service_name, 
        "【演示告警】服务恢复后的新故障"
    )
    
    count = monitor.alert_counters.get(service_name, 0)
    print(f"✓ 新告警发送: {'成功' if alert_sent else '失败'}")
    print(f"✓ 新告警计数: {count}")
    
    print()
    
    # 演示场景3：多服务独立计数
    print("🎬 场景3: 多服务独立计数")
    print("-" * 40)
    
    # 创建多服务配置
    multi_config = """# 多服务演示配置
CHECK_INTERVAL=10
MAX_ALERT_COUNT=2
DING_API_URL=
SERVICES=SERVICE_A SERVICE_B

SERVICE_A_NAME=服务A
SERVICE_A_JAR_PATH=/tmp/serviceA.jar
SERVICE_A_JAVA_OPTS=
SERVICE_A_PROCESS_KEYWORD=serviceA

SERVICE_B_NAME=服务B
SERVICE_B_JAR_PATH=/tmp/serviceB.jar
SERVICE_B_JAVA_OPTS=
SERVICE_B_PROCESS_KEYWORD=serviceB
"""
    
    with open('demo-multi-alert.conf', 'w', encoding='utf-8') as f:
        f.write(multi_config)
    
    multi_monitor = monitor_services.ServiceMonitor('demo-multi-alert.conf')
    
    services = ["服务A", "服务B"]
    
    print("\n测试两个服务的独立告警计数:")
    
    for service in services:
        print(f"\n{service} 告警测试:")
        
        for i in range(3):
            alert_sent = multi_monitor.send_alert_with_limit(
                service, 
                f"【{service}告警】故障第{i+1}次"
            )
            count = multi_monitor.alert_counters.get(service, 0)
            silenced = multi_monitor.is_alert_silenced(service)
            
            print(f"  第{i+1}次: 发送{'成功' if alert_sent else '失败'}, 计数:{count}, 静默:{silenced}")
    
    print()
    
    # 显示最终状态
    print("📊 最终状态总结:")
    print("-" * 40)
    
    for service in services:
        count = multi_monitor.alert_counters.get(service, 0)
        silenced = multi_monitor.is_alert_silenced(service)
        print(f"{service}: 告警计数={count}, 静默状态={'是' if silenced else '否'}")

def show_configuration():
    """显示配置说明"""
    print("\n⚙️ 配置说明:")
    print("-" * 40)
    print("在配置文件中添加以下参数:")
    print()
    print("# 最大告警次数（默认5次）")
    print("MAX_ALERT_COUNT=5")
    print()
    print("配置说明:")
    print("• MAX_ALERT_COUNT: 每个服务连续告警的最大次数")
    print("• 超过此次数后，服务告警将被静默")
    print("• 服务恢复正常后，告警计数自动重置")
    print("• 不同服务的告警计数相互独立")

def show_usage():
    """显示使用方法"""
    print("\n📖 使用方法:")
    print("-" * 40)
    print("1. 在配置文件中设置 MAX_ALERT_COUNT")
    print("2. 正常运行监控脚本")
    print("3. 监控脚本会自动管理告警计数")
    print("4. 无需手动干预，自动静默和重置")
    print()
    print("告警流程:")
    print("服务故障 → 发送告警 → 计数+1 → 达到上限 → 静默告警")
    print("服务恢复 → 重置计数 → 恢复正常告警")

def main():
    """主函数"""
    try:
        demo_alert_limit()
        show_configuration()
        show_usage()
        
        print("\n🎉 告警限制功能演示完成!")
        print("\n✨ 功能特点:")
        print("• 防止告警轰炸，避免过度通知")
        print("• 自动管理告警状态，无需人工干预")
        print("• 多服务独立计数，精确控制")
        print("• 服务恢复时自动重置，确保后续告警正常")
        
    except KeyboardInterrupt:
        print("\n\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中发生错误: {e}")

if __name__ == "__main__":
    main()
