# Python服务监控系统使用说明

## 概述

`monitor-services.py` 是一个功能强大的Python服务监控系统，用于自动监控Java服务进程，在服务故障时自动重启，并通过钉钉发送通知。该系统具有告警限制功能，可以防止告警轰炸。

## 主要功能

### 🔍 服务监控
- 自动检测Java服务进程状态
- 支持多服务同时监控
- 基于进程关键字精确识别服务
- 跨平台支持（Linux/macOS/Windows）

### 🔄 自动重启
- 服务故障时自动重启
- 智能等待服务启动完成
- 重启失败时发送告警通知
- 支持自定义Java启动参数

### 📢 钉钉通知
- 服务故障告警通知
- 服务恢复成功通知
- 重启失败告警通知
- 支持自定义通知内容

### 🛡️ 告警限制
- 防止告警轰炸，避免过度通知
- 每个服务独立计数，互不影响
- 服务恢复时自动重置告警计数
- 可配置最大告警次数

### 📝 日志管理
- 详细的运行日志记录
- 智能日志清理功能（保留7天内日志）
- 支持不同日志级别
- 自动清理旧日志内容，保留最新日志

## 安装依赖

```bash
pip install -r requirements.txt
```

依赖包：
- `requests>=2.25.0` - HTTP请求库，用于钉钉通知
- `psutil>=5.8.0` - 系统进程管理库

## 配置文件

### 基本配置格式

```bash
# 检查间隔（秒）
CHECK_INTERVAL=60

# 最大告警次数（防止告警轰炸）
MAX_ALERT_COUNT=5

# 钉钉通知配置
DING_API_URL=https://oapi.dingtalk.com/robot/send?access_token=your_token

# 监控的服务列表
SERVICES=GATEWAY AUTH SYSTEM CUSTOMER

# 服务配置示例
GATEWAY_NAME=网关服务
GATEWAY_JAR_PATH=/app/gateway/gateway.jar
GATEWAY_JAVA_OPTS=-Xms512m -Xmx1024m -Dspring.profiles.active=prod
GATEWAY_PROCESS_KEYWORD=gateway.jar

AUTH_NAME=认证服务
AUTH_JAR_PATH=/app/auth/auth.jar
AUTH_JAVA_OPTS=-Xms256m -Xmx512m
AUTH_PROCESS_KEYWORD=auth.jar
```

### 配置参数说明

| 参数 | 说明 | 默认值 | 必填 |
|------|------|--------|------|
| `CHECK_INTERVAL` | 检查间隔（秒） | 60 | 否 |
| `MAX_ALERT_COUNT` | 最大告警次数 | 5 | 否 |
| `DING_API_URL` | 钉钉机器人URL | 无 | 否 |
| `SERVICES` | 监控服务列表 | 无 | 是 |
| `{SERVICE}_NAME` | 服务显示名称 | 无 | 是 |
| `{SERVICE}_JAR_PATH` | JAR文件路径 | 无 | 是 |
| `{SERVICE}_JAVA_OPTS` | Java启动参数 | 无 | 否 |
| `{SERVICE}_PROCESS_KEYWORD` | 进程关键字 | 无 | 是 |

## 使用方法

### 基本用法

```bash
# 使用默认配置文件
python3 monitor-services.py

# 指定配置文件
python3 monitor-services.py /path/to/config.conf

# 显示帮助信息
python3 monitor-services.py --help

# 显示版本信息
python3 monitor-services.py --version
```

### 后台运行

```bash
# 使用nohup后台运行
nohup python3 monitor-services.py > monitor.log 2>&1 &

# 使用systemd服务（推荐）
sudo systemctl start monitor-services
sudo systemctl enable monitor-services
```

### 停止监控

```bash
# 使用Ctrl+C优雅停止
# 或发送SIGTERM信号
kill -TERM <pid>
```

## 告警限制功能

### 工作原理

1. **告警计数**：每个服务独立计数告警次数
2. **达到限制**：超过最大告警次数后自动静默
3. **静默通知**：发送静默提醒，避免遗漏
4. **自动重置**：服务恢复时自动重置计数

### 告警流程

```
服务故障 → 发送告警(第1次) → 计数+1
    ↓
继续故障 → 发送告警(第2次) → 计数+1
    ↓
...
    ↓
达到上限 → 发送静默通知 → 后续告警被静默
    ↓
服务恢复 → 重置计数 → 恢复正常告警
```

### 配置示例

```bash
# 设置最大告警次数为3次
MAX_ALERT_COUNT=3
```

## 日志说明

### 日志位置
- 默认：`/var/log/service-monitor.log`
- 备选：`./service-monitor.log`（权限不足时）

### 日志级别
- `INFO`：正常运行信息
- `WARNING`：警告信息（如服务故障）
- `ERROR`：错误信息（如重启失败）

### 日志示例

```
[2025-07-02 10:25:08] 加载配置完成，监控服务: GATEWAY, AUTH
[2025-07-02 10:25:08] 最大告警次数: 5次
[2025-07-02 10:25:09] 服务 GATEWAY 正在运行
[2025-07-02 10:25:09] 警告: 服务 AUTH 未运行，正在重启...
[2025-07-02 10:25:24] 服务 AUTH 已成功重启
[2025-07-02 10:25:24] 服务 AUTH 告警计数已重置
```

## 钉钉通知配置

### 获取钉钉机器人URL

1. 在钉钉群中添加自定义机器人
2. 选择"自定义"机器人类型
3. 设置安全设置（推荐使用关键词）
4. 复制Webhook地址

### 通知内容示例

```
【服务告警】服务 网关服务 已停止运行，正在尝试自动重启... (第1次告警)
【服务恢复】服务 网关服务 已成功自动重启并正常运行
【告警静默】服务 网关服务 已连续告警5次，后续告警将被静默，请及时处理！
```

## 故障排除

### 常见问题

#### 1. 服务检测不到
**问题**：监控脚本显示服务未运行，但实际服务正常
**解决**：
- 检查 `PROCESS_KEYWORD` 是否正确
- 确认关键字在进程命令行中存在
- 使用 `ps aux | grep keyword` 验证

#### 2. 服务重启失败
**问题**：服务重启命令执行失败
**解决**：
- 检查JAR文件路径是否正确
- 确认Java环境变量配置
- 验证文件权限和目录权限

#### 3. 钉钉通知不发送
**问题**：配置了钉钉URL但收不到通知
**解决**：
- 验证钉钉机器人URL是否正确
- 检查网络连接
- 确认机器人安全设置

#### 4. 告警计数异常
**问题**：告警计数没有按预期工作
**解决**：
- 检查服务名称是否唯一
- 确认 `MAX_ALERT_COUNT` 配置正确
- 查看日志中的计数记录

### 调试方法

```bash
# 查看详细日志
tail -f /var/log/service-monitor.log

# 检查进程
ps aux | grep monitor-services

# 测试配置
python3 test_monitor.py

# 验证告警限制
python3 test_alert_limit.py
```

## 最佳实践

### 1. 配置建议
- 设置合理的检查间隔（建议30-120秒）
- 根据服务重要性调整告警次数限制
- 使用具体的进程关键字避免误判

### 2. 监控建议
- 定期检查监控脚本运行状态
- 关注日志中的异常信息
- 及时处理被静默的服务

### 3. 部署建议
- 使用systemd管理监控进程
- 配置日志轮转避免磁盘占满
- 设置监控脚本的监控（监控的监控）

## 版本信息

- **当前版本**：v2.0
- **主要更新**：新增告警限制功能
- **兼容性**：完全兼容v1.x配置文件

## 技术支持

如遇到问题，请：
1. 查看日志文件获取详细错误信息
2. 运行测试脚本验证功能
3. 检查配置文件格式和参数
4. 参考故障排除章节

## 示例配置文件

### 完整配置示例

```bash
# ================================
# 服务监控配置文件
# ================================

# 基本配置
CHECK_INTERVAL=60
MAX_ALERT_COUNT=5
DING_API_URL=https://oapi.dingtalk.com/robot/send?access_token=your_token_here

# 监控服务列表
SERVICES=GATEWAY AUTH SYSTEM CUSTOMER ERP THIRD JOB XXL_JOB

# ================================
# 网关服务配置
# ================================
GATEWAY_NAME=API网关服务
GATEWAY_JAR_PATH=/app/gateway/gateway-service.jar
GATEWAY_JAVA_OPTS=-Xms1g -Xmx2g -XX:+UseG1GC -Dspring.profiles.active=prod
GATEWAY_PROCESS_KEYWORD=gateway-service.jar

# ================================
# 认证服务配置
# ================================
AUTH_NAME=用户认证服务
AUTH_JAR_PATH=/app/auth/auth-service.jar
AUTH_JAVA_OPTS=-Xms512m -Xmx1g -Dspring.profiles.active=prod
AUTH_PROCESS_KEYWORD=auth-service.jar

# ================================
# 系统服务配置
# ================================
SYSTEM_NAME=系统管理服务
SYSTEM_JAR_PATH=/app/system/system-service.jar
SYSTEM_JAVA_OPTS=-Xms256m -Xmx512m
SYSTEM_PROCESS_KEYWORD=system-service.jar

# ================================
# 客户服务配置
# ================================
CUSTOMER_NAME=客户管理服务
CUSTOMER_JAR_PATH=/app/customer/customer-service.jar
CUSTOMER_JAVA_OPTS=-Xms512m -Xmx1g
CUSTOMER_PROCESS_KEYWORD=customer-service.jar
```

### 测试配置示例

```bash
# 测试环境配置
CHECK_INTERVAL=10
MAX_ALERT_COUNT=3
DING_API_URL=
SERVICES=TEST_SERVICE

TEST_SERVICE_NAME=测试服务
TEST_SERVICE_JAR_PATH=/tmp/test.jar
TEST_SERVICE_JAVA_OPTS=
TEST_SERVICE_PROCESS_KEYWORD=test-nonexistent
```

## 系统集成

### Systemd服务配置

创建服务文件 `/etc/systemd/system/monitor-services.service`：

```ini
[Unit]
Description=Java Services Monitor
After=network.target

[Service]
Type=simple
User=monitor
Group=monitor
WorkingDirectory=/opt/monitor
ExecStart=/usr/bin/python3 /opt/monitor/monitor-services.py /opt/monitor/service-monitor.conf
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

启用和启动服务：

```bash
sudo systemctl daemon-reload
sudo systemctl enable monitor-services
sudo systemctl start monitor-services
sudo systemctl status monitor-services
```

### 日志轮转配置

创建 `/etc/logrotate.d/monitor-services`：

```
/var/log/service-monitor.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 monitor monitor
    postrotate
        systemctl reload monitor-services
    endscript
}
```

### Crontab备份方案

```bash
# 每天备份监控日志
0 2 * * * cp /var/log/service-monitor.log /backup/monitor-$(date +\%Y\%m\%d).log

# 每周清理旧备份
0 3 * * 0 find /backup -name "monitor-*.log" -mtime +30 -delete
```

## 高级功能

### 自定义通知模板

可以通过修改代码自定义通知内容：

```python
def send_custom_notification(self, service_name, status, details=""):
    """发送自定义通知"""
    templates = {
        'failure': f"🚨 【紧急告警】\n服务：{service_name}\n状态：服务异常\n详情：{details}\n时间：{datetime.now()}",
        'recovery': f"✅ 【恢复通知】\n服务：{service_name}\n状态：服务恢复\n时间：{datetime.now()}",
        'silence': f"🔇 【告警静默】\n服务：{service_name}\n说明：告警已达上限，进入静默模式\n时间：{datetime.now()}"
    }

    content = templates.get(status, f"服务 {service_name} 状态更新：{details}")
    self.send_ding_notification(content)
```

### 监控指标扩展

```python
def collect_metrics(self, service_name):
    """收集服务指标"""
    metrics = {
        'cpu_usage': self.get_cpu_usage(service_name),
        'memory_usage': self.get_memory_usage(service_name),
        'uptime': self.get_uptime(service_name),
        'restart_count': self.get_restart_count(service_name)
    }
    return metrics
```

## 性能优化

### 1. 进程检测优化
- 使用进程PID缓存减少系统调用
- 优化进程关键字匹配算法
- 批量处理多个服务检测

### 2. 内存使用优化
- 定期清理告警计数器
- 限制日志缓存大小
- 使用生成器处理大量数据

### 3. 网络请求优化
- 钉钉通知请求超时设置
- 失败重试机制
- 异步通知发送

## 安全考虑

### 1. 权限控制
- 使用专用用户运行监控脚本
- 限制配置文件访问权限
- JAR文件执行权限管理

### 2. 网络安全
- 钉钉机器人Token保护
- HTTPS通信加密
- 网络访问白名单

### 3. 日志安全
- 敏感信息脱敏
- 日志文件权限控制
- 定期清理历史日志

## 扩展开发

### 添加新的通知渠道

```python
class NotificationManager:
    def __init__(self):
        self.channels = {
            'dingtalk': DingTalkNotifier(),
            'email': EmailNotifier(),
            'slack': SlackNotifier(),
            'webhook': WebhookNotifier()
        }

    def send_notification(self, channel, message):
        if channel in self.channels:
            self.channels[channel].send(message)
```

### 添加健康检查接口

```python
def health_check_endpoint(self):
    """提供HTTP健康检查接口"""
    from http.server import HTTPServer, BaseHTTPRequestHandler

    class HealthHandler(BaseHTTPRequestHandler):
        def do_GET(self):
            if self.path == '/health':
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                status = {
                    'status': 'healthy',
                    'services': self.get_all_service_status(),
                    'timestamp': datetime.now().isoformat()
                }
                self.wfile.write(json.dumps(status).encode())
```

## 项目文件结构

```
python-space/
├── monitor-services.py              # 主监控脚本
├── requirements.txt                 # Python依赖包
├── service-monitor-python.conf      # 主配置文件
├── monitor-services使用说明.md      # 完整使用说明（本文档）
├── 告警限制功能说明.md              # 告警限制功能详细说明
├──
├── # 测试和演示文件
├── test_monitor.py                  # 基础功能测试
├── test_alert_limit.py              # 告警限制功能测试
├── demo_monitor.py                  # 功能演示脚本
├── demo_alert_limit.py              # 告警限制演示
├── quick_test_alert_limit.py        # 快速测试脚本
├──
├── # 配置文件示例
├── test-config.conf                 # 测试配置
├── demo-config.conf                 # 演示配置
├── quick-test.conf                  # 快速测试配置
├──
├── # 原始bash版本（参考）
└── monitor/
    ├── monitor-services.sh          # 原始bash脚本
    ├── service-monitor.conf         # bash版本配置
    ├── service-monitor.service      # systemd服务文件
    └── README.md                    # bash版本说明
```

## 快速开始

### 1. 基础部署

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 复制配置文件
cp service-monitor-python.conf my-monitor.conf

# 3. 编辑配置文件
vim my-monitor.conf

# 4. 运行监控
python3 monitor-services.py my-monitor.conf
```

### 2. 生产环境部署

```bash
# 1. 创建专用用户
sudo useradd -r -s /bin/false monitor

# 2. 创建工作目录
sudo mkdir -p /opt/monitor
sudo chown monitor:monitor /opt/monitor

# 3. 部署文件
sudo cp monitor-services.py /opt/monitor/
sudo cp requirements.txt /opt/monitor/
sudo cp service-monitor-python.conf /opt/monitor/monitor.conf

# 4. 安装依赖
cd /opt/monitor
sudo -u monitor pip install -r requirements.txt

# 5. 配置systemd服务
sudo cp monitor-services.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable monitor-services
sudo systemctl start monitor-services
```

### 3. 验证部署

```bash
# 检查服务状态
sudo systemctl status monitor-services

# 查看日志
sudo journalctl -u monitor-services -f

# 测试功能
python3 test_monitor.py
python3 test_alert_limit.py
```

## 常用命令

```bash
# 启动监控
python3 monitor-services.py

# 后台运行
nohup python3 monitor-services.py > monitor.log 2>&1 &

# 查看帮助
python3 monitor-services.py --help

# 查看版本
python3 monitor-services.py --version

# 测试配置
python3 test_monitor.py

# 演示功能
python3 demo_monitor.py
python3 demo_alert_limit.py

# 检查日志
tail -f /var/log/service-monitor.log

# 检查进程
ps aux | grep monitor-services
```

## 更新日志

### v2.0 (2025-07-02)
- ✨ 新增告警限制功能，防止告警轰炸
- ✨ 支持每个服务独立告警计数
- ✨ 服务恢复时自动重置告警计数
- ✨ 可配置最大告警次数
- ✨ 完善的测试和演示脚本
- ✨ 详细的使用说明文档
- 🐛 修复日志权限问题
- 🐛 优化进程检测逻辑
- 📝 完善帮助信息和版本显示

### v1.0 (2025-07-01)
- ✨ 从bash版本迁移到Python
- ✨ 支持多服务监控
- ✨ 钉钉通知集成
- ✨ 自动重启功能
- ✨ 详细日志记录
- ✨ 跨平台支持

---

*该文档涵盖了monitor-services.py的所有功能和使用方法，包括基础使用、高级配置、系统集成和扩展开发。如有疑问请参考相关测试脚本和示例配置。*
