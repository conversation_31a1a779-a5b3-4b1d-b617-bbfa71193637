#!/bin/bash
# 监控系统配置向导
# 帮助用户快速配置监控服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

CONFIG_FILE="/opt/monitor/monitor.conf"
BACKUP_DIR="/opt/monitor/backup/config-backup"

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查权限
check_permission() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root权限运行此脚本"
        exit 1
    fi
}

# 备份现有配置
backup_config() {
    if [ -f "$CONFIG_FILE" ]; then
        local backup_file="$BACKUP_DIR/monitor.conf.$(date +%Y%m%d_%H%M%S)"
        cp "$CONFIG_FILE" "$backup_file"
        log_info "已备份现有配置到: $backup_file"
    fi
}

# 获取用户输入
get_basic_config() {
    echo ""
    echo "=========================================="
    echo "基础配置"
    echo "=========================================="
    
    # 检查间隔
    echo -n "请输入检查间隔（秒，默认60）: "
    read CHECK_INTERVAL
    CHECK_INTERVAL=${CHECK_INTERVAL:-60}
    
    # 最大告警次数
    echo -n "请输入最大告警次数（默认5）: "
    read MAX_ALERT_COUNT
    MAX_ALERT_COUNT=${MAX_ALERT_COUNT:-5}
    
    # 钉钉通知
    echo -n "请输入钉钉机器人Webhook URL（可选）: "
    read DING_API_URL
}

# 获取服务配置
get_services_config() {
    echo ""
    echo "=========================================="
    echo "服务配置"
    echo "=========================================="
    
    SERVICES=()
    SERVICE_CONFIGS=()
    
    while true; do
        echo ""
        echo "添加监控服务 #$((${#SERVICES[@]} + 1))"
        echo "----------------------------------------"
        
        # 服务标识
        echo -n "服务标识（如：GATEWAY，用于配置前缀）: "
        read SERVICE_ID
        
        if [ -z "$SERVICE_ID" ]; then
            break
        fi
        
        # 转换为大写
        SERVICE_ID=$(echo "$SERVICE_ID" | tr '[:lower:]' '[:upper:]')
        
        # 服务名称
        echo -n "服务显示名称（如：API网关服务）: "
        read SERVICE_NAME
        
        # JAR路径
        echo -n "JAR文件路径: "
        read JAR_PATH
        
        # Java参数
        echo -n "Java启动参数（可选）: "
        read JAVA_OPTS
        
        # 进程关键字
        echo -n "进程关键字（用于识别进程）: "
        read PROCESS_KEYWORD
        
        # 验证必填项
        if [ -z "$SERVICE_NAME" ] || [ -z "$JAR_PATH" ] || [ -z "$PROCESS_KEYWORD" ]; then
            log_warning "服务名称、JAR路径和进程关键字不能为空，跳过此服务"
            continue
        fi
        
        # 添加到数组
        SERVICES+=("$SERVICE_ID")
        SERVICE_CONFIGS+=("$SERVICE_ID|$SERVICE_NAME|$JAR_PATH|$JAVA_OPTS|$PROCESS_KEYWORD")
        
        log_success "已添加服务: $SERVICE_NAME"
        
        echo -n "是否继续添加服务？(y/N): "
        read CONTINUE
        if [[ ! "$CONTINUE" =~ ^[Yy]$ ]]; then
            break
        fi
    done
    
    if [ ${#SERVICES[@]} -eq 0 ]; then
        log_warning "未配置任何服务，将使用示例配置"
        # 添加示例服务
        SERVICES=("EXAMPLE")
        SERVICE_CONFIGS=("EXAMPLE|示例服务|/app/example/example.jar|-Xms512m -Xmx1g|example.jar")
    fi
}

# 生成配置文件
generate_config() {
    echo ""
    log_info "生成配置文件..."
    
    cat > "$CONFIG_FILE" << EOF
# ================================
# Python服务监控系统配置文件
# 生成时间: $(date)
# ================================

# 基本配置
CHECK_INTERVAL=$CHECK_INTERVAL
MAX_ALERT_COUNT=$MAX_ALERT_COUNT
DING_API_URL=$DING_API_URL

# 监控服务列表
SERVICES=$(IFS=' '; echo "${SERVICES[*]}")

EOF

    # 添加服务配置
    for config in "${SERVICE_CONFIGS[@]}"; do
        IFS='|' read -r service_id service_name jar_path java_opts process_keyword <<< "$config"
        
        cat >> "$CONFIG_FILE" << EOF
# ================================
# $service_name 配置
# ================================
${service_id}_NAME=$service_name
${service_id}_JAR_PATH=$jar_path
${service_id}_JAVA_OPTS=$java_opts
${service_id}_PROCESS_KEYWORD=$process_keyword

EOF
    done
    
    # 设置权限
    chown monitor:monitor "$CONFIG_FILE"
    chmod 644 "$CONFIG_FILE"
    
    log_success "配置文件生成完成: $CONFIG_FILE"
}

# 显示配置摘要
show_config_summary() {
    echo ""
    echo "=========================================="
    echo "配置摘要"
    echo "=========================================="
    echo "检查间隔: $CHECK_INTERVAL 秒"
    echo "最大告警次数: $MAX_ALERT_COUNT 次"
    echo "钉钉通知: ${DING_API_URL:-未配置}"
    echo "监控服务数量: ${#SERVICES[@]}"
    echo ""
    echo "监控服务列表:"
    for config in "${SERVICE_CONFIGS[@]}"; do
        IFS='|' read -r service_id service_name jar_path java_opts process_keyword <<< "$config"
        echo "• $service_name ($service_id)"
        echo "  JAR: $jar_path"
        echo "  关键字: $process_keyword"
    done
    echo ""
}

# 测试配置
test_config() {
    echo ""
    log_info "测试配置文件..."
    
    if sudo -u monitor python3 /opt/monitor/monitor-services.py "$CONFIG_FILE" --help > /dev/null 2>&1; then
        log_success "配置文件语法正确"
    else
        log_error "配置文件语法错误，请检查"
        return 1
    fi
}

# 重启服务
restart_service() {
    echo ""
    log_info "重启监控服务..."
    
    if systemctl is-active --quiet monitor-services; then
        systemctl restart monitor-services
        sleep 2
        if systemctl is-active --quiet monitor-services; then
            log_success "监控服务重启成功"
        else
            log_error "监控服务重启失败"
            systemctl status monitor-services
            return 1
        fi
    else
        log_info "监控服务未运行，正在启动..."
        systemctl start monitor-services
        sleep 2
        if systemctl is-active --quiet monitor-services; then
            log_success "监控服务启动成功"
        else
            log_error "监控服务启动失败"
            systemctl status monitor-services
            return 1
        fi
    fi
}

# 显示后续操作
show_next_steps() {
    echo ""
    echo "=========================================="
    log_success "配置完成！"
    echo "=========================================="
    echo ""
    echo "后续操作："
    echo "1. 查看服务状态: systemctl status monitor-services"
    echo "2. 查看实时日志: journalctl -u monitor-services -f"
    echo "3. 查看应用日志: tail -f /opt/monitor/logs/service-monitor.log"
    echo "4. 编辑配置文件: vim $CONFIG_FILE"
    echo "5. 重新加载配置: systemctl reload monitor-services"
    echo ""
    echo "测试命令："
    echo "• 手动运行测试: sudo -u monitor python3 /opt/monitor/monitor-services.py $CONFIG_FILE"
    echo "• 运行功能测试: python3 /opt/monitor/scripts/test_monitor.py"
    echo ""
}

# 主函数
main() {
    echo "=========================================="
    echo "Python服务监控系统配置向导"
    echo "=========================================="
    echo ""
    echo "此向导将帮助您配置监控系统"
    echo ""
    
    # 检查权限
    check_permission
    
    # 检查是否已部署
    if [ ! -f "/opt/monitor/monitor-services.py" ]; then
        log_error "监控系统未部署，请先运行 deploy-monitor.sh"
        exit 1
    fi
    
    # 备份现有配置
    backup_config
    
    # 获取配置
    get_basic_config
    get_services_config
    
    # 显示摘要
    show_config_summary
    
    # 确认生成
    echo -n "是否生成配置文件？(Y/n): "
    read CONFIRM
    if [[ "$CONFIRM" =~ ^[Nn]$ ]]; then
        log_info "配置已取消"
        exit 0
    fi
    
    # 生成配置
    generate_config
    
    # 测试配置
    if ! test_config; then
        log_error "配置测试失败，请检查配置"
        exit 1
    fi
    
    # 重启服务
    if ! restart_service; then
        log_error "服务重启失败，请手动检查"
        exit 1
    fi
    
    # 显示后续操作
    show_next_steps
}

# 执行主函数
main "$@"
