# Python版本服务监控配置文件
# 注意：这个配置文件与原bash版本兼容

# 检查间隔（秒）
CHECK_INTERVAL=60

# 最大告警次数（每个服务连续告警超过此次数后将被静默）
MAX_ALERT_COUNT=5

# 钉钉通知配置
DING_API_URL=https://oapi.dingtalk.com/robot/send?access_token=fc714da19e69cf75d6efe88686ee795e47b1909f9e188cca2c8578fffae95e0a

# 要监控的服务列表（空格分隔）
SERVICES=GATEWAY AUTH SYSTEM CUSTOMER ERP THIRD JOB XXL_JOB

# Gateway 服务配置
GATEWAY_NAME=nnb-gateway 服务
GATEWAY_JAR_PATH=/home/<USER>/data/crm/java/nnb-gateway.jar
GATEWAY_JAVA_OPTS=
GATEWAY_PROCESS_KEYWORD=nnb-gateway.jar

# Auth 服务配置
AUTH_NAME=nnb-auth 服务
AUTH_JAR_PATH=/home/<USER>/data/crm/java/nnb-auth.jar
AUTH_JAVA_OPTS=
AUTH_PROCESS_KEYWORD=nnb-auth.jar

# System 服务配置
SYSTEM_NAME=nnb-modules-system 服务
SYSTEM_JAR_PATH=/home/<USER>/data/crm/java/nnb-modules-system.jar
SYSTEM_JAVA_OPTS=
SYSTEM_PROCESS_KEYWORD=nnb-modules-system.jar

# CUSTOMER 服务配置
CUSTOMER_NAME=nnb-modules-customer 服务
CUSTOMER_JAR_PATH=/home/<USER>/data/crm/java/nnb-modules-customer.jar
CUSTOMER_JAVA_OPTS=
CUSTOMER_PROCESS_KEYWORD=nnb-modules-customer.jar

# ERP 服务配置
ERP_NAME=nnb-modules-erp 服务
ERP_JAR_PATH=/home/<USER>/data/crm/java/nnb-modules-erp.jar.jar
ERP_JAVA_OPTS=
ERP_PROCESS_KEYWORD=nnb-modules-erp.jar

# THIRD 服务配置
THIRD_NAME=nnb-modules-third 服务
THIRD_JAR_PATH=/home/<USER>/data/crm/java/nnb-modules-third.jar
THIRD_JAVA_OPTS=
THIRD_PROCESS_KEYWORD=nnb-modules-third.jar

# JOB 服务配置
JOB_NAME=nnb-modules-job 服务
JOB_JAR_PATH=/home/<USER>/data/crm/java/nnb-modules-job.jar
JOB_JAVA_OPTS=
JOB_PROCESS_KEYWORD=nnb-modules-job.jar

# XXL_JOB 服务配置
XXL_JOB_NAME=xxl-job 服务
XXL_JOB_JAR_PATH=/home/<USER>/data/xxl-job/xxl-job-admin-2.4.0.jar
XXL_JOB_JAVA_OPTS=
XXL_JOB_PROCESS_KEYWORD=xxl-job-admin-2.4.0.jar
