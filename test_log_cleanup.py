#!/usr/bin/env python3
"""
测试日志清理功能
"""

import os
import time
import datetime
import importlib.util

# 动态导入monitor-services.py
spec = importlib.util.spec_from_file_location("monitor_services", "monitor-services.py")
monitor_services = importlib.util.module_from_spec(spec)
spec.loader.exec_module(monitor_services)

def create_test_log_with_old_entries():
    """创建包含新旧日志条目的测试日志文件"""
    test_log_path = "./test-cleanup.log"
    
    # 生成不同时间的日志条目
    now = datetime.datetime.now()
    
    log_entries = []
    
    # 添加10天前的日志（应该被清理）
    old_date = now - datetime.timedelta(days=10)
    log_entries.append(f"[{old_date.strftime('%Y-%m-%d %H:%M:%S')}] 这是10天前的日志，应该被清理\n")
    log_entries.append(f"[{old_date.strftime('%Y-%m-%d %H:%M:%S')}] 另一条10天前的日志\n")
    
    # 添加5天前的日志（应该被清理）
    old_date2 = now - datetime.timedelta(days=8)
    log_entries.append(f"[{old_date2.strftime('%Y-%m-%d %H:%M:%S')}] 这是8天前的日志，应该被清理\n")
    
    # 添加3天前的日志（应该保留）
    recent_date = now - datetime.timedelta(days=3)
    log_entries.append(f"[{recent_date.strftime('%Y-%m-%d %H:%M:%S')}] 这是3天前的日志，应该保留\n")
    
    # 添加1天前的日志（应该保留）
    recent_date2 = now - datetime.timedelta(days=1)
    log_entries.append(f"[{recent_date2.strftime('%Y-%m-%d %H:%M:%S')}] 这是1天前的日志，应该保留\n")
    
    # 添加今天的日志（应该保留）
    log_entries.append(f"[{now.strftime('%Y-%m-%d %H:%M:%S')}] 这是今天的日志，应该保留\n")
    log_entries.append(f"[{now.strftime('%Y-%m-%d %H:%M:%S')}] 另一条今天的日志\n")
    
    # 添加一些没有时间戳的行（应该保留）
    log_entries.append("这是没有时间戳的日志行，应该保留\n")
    log_entries.append("    这是缩进的日志行，可能是多行日志的续行\n")
    
    # 写入测试日志文件
    with open(test_log_path, 'w', encoding='utf-8') as f:
        f.writelines(log_entries)
    
    print(f"✓ 创建测试日志文件: {test_log_path}")
    print(f"  总共 {len(log_entries)} 行日志")
    print(f"  包含 3 行旧日志（应该被清理）")
    print(f"  包含 6 行新日志（应该保留）")
    
    return test_log_path

def test_log_cleanup():
    """测试日志清理功能"""
    print("=== 日志清理功能测试 ===\n")
    
    # 创建测试日志
    test_log_path = create_test_log_with_old_entries()
    
    # 显示清理前的日志内容
    print("\n📋 清理前的日志内容:")
    print("-" * 50)
    with open(test_log_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        for i, line in enumerate(lines, 1):
            print(f"{i:2d}: {line.rstrip()}")
    
    print(f"\n清理前总行数: {len(lines)}")
    
    # 创建监控器实例（用于测试日志清理功能）
    config_content = """CHECK_INTERVAL=5
MAX_ALERT_COUNT=3
DING_API_URL=
SERVICES=TEST

TEST_NAME=测试服务
TEST_JAR_PATH=/tmp/test.jar
TEST_JAVA_OPTS=
TEST_PROCESS_KEYWORD=test-nonexistent
"""
    
    with open('test-cleanup.conf', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    monitor = monitor_services.ServiceMonitor('test-cleanup.conf')
    
    # 测试日志清理功能
    print("\n🧹 执行日志清理...")
    seven_days_seconds = 7 * 24 * 3600
    cleaned_lines = monitor._clean_log_file_content(test_log_path, seven_days_seconds)
    
    print(f"✓ 清理完成，删除了 {cleaned_lines} 行旧日志")
    
    # 显示清理后的日志内容
    print("\n📋 清理后的日志内容:")
    print("-" * 50)
    with open(test_log_path, 'r', encoding='utf-8') as f:
        lines_after = f.readlines()
        for i, line in enumerate(lines_after, 1):
            print(f"{i:2d}: {line.rstrip()}")
    
    print(f"\n清理后总行数: {len(lines_after)}")
    print(f"删除行数: {len(lines) - len(lines_after) + 1}")  # +1 因为添加了清理记录
    
    # 验证结果
    print("\n✅ 验证结果:")
    
    # 检查是否保留了新日志
    recent_logs = [line for line in lines_after if "应该保留" in line]
    old_logs = [line for line in lines_after if "应该被清理" in line]
    no_timestamp_logs = [line for line in lines_after if not line.startswith('[') and "清理" not in line]
    
    print(f"• 保留的新日志: {len(recent_logs)} 行 ({'✓' if len(recent_logs) == 4 else '✗'})")
    print(f"• 残留的旧日志: {len(old_logs)} 行 ({'✓' if len(old_logs) == 0 else '✗'})")
    print(f"• 保留的无时间戳日志: {len(no_timestamp_logs)} 行 ({'✓' if len(no_timestamp_logs) == 2 else '✗'})")
    print(f"• 添加的清理记录: {'✓' if any('日志清理' in line for line in lines_after) else '✗'}")
    
    # 清理测试文件
    os.remove(test_log_path)
    os.remove('test-cleanup.conf')
    print(f"\n🗑️ 清理测试文件")

def test_cleanup_timing():
    """测试清理时机控制"""
    print("\n=== 清理时机控制测试 ===\n")
    
    # 创建配置
    config_content = """CHECK_INTERVAL=5
MAX_ALERT_COUNT=3
DING_API_URL=
SERVICES=TEST

TEST_NAME=测试服务
TEST_JAR_PATH=/tmp/test.jar
TEST_JAVA_OPTS=
TEST_PROCESS_KEYWORD=test-nonexistent
"""
    
    with open('test-timing.conf', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    monitor = monitor_services.ServiceMonitor('test-timing.conf')
    
    # 测试1: 首次运行应该执行清理
    print("📋 测试1: 首次运行清理检查")
    
    # 删除清理标记文件（如果存在）
    if os.path.exists(monitor.log_cleanup_flag):
        os.remove(monitor.log_cleanup_flag)
    
    # 模拟清理检查
    current_time = time.time()
    cleanup_flag_time = 0
    
    if os.path.exists(monitor.log_cleanup_flag):
        with open(monitor.log_cleanup_flag, 'r') as f:
            cleanup_flag_time = float(f.read().strip())
    
    time_diff = current_time - cleanup_flag_time
    seven_days_seconds = 7 * 24 * 3600
    
    should_cleanup = time_diff > seven_days_seconds
    print(f"• 时间差: {time_diff:.0f} 秒")
    print(f"• 7天阈值: {seven_days_seconds} 秒")
    print(f"• 应该清理: {'是' if should_cleanup else '否'} ({'✓' if should_cleanup else '✗'})")
    
    # 测试2: 创建清理标记文件，测试不应该清理
    print("\n📋 测试2: 最近清理过，不应该再次清理")
    
    with open(monitor.log_cleanup_flag, 'w') as f:
        f.write(str(current_time))
    
    cleanup_flag_time = current_time
    time_diff = current_time - cleanup_flag_time
    should_cleanup = time_diff > seven_days_seconds
    
    print(f"• 时间差: {time_diff:.0f} 秒")
    print(f"• 应该清理: {'是' if should_cleanup else '否'} ({'✓' if not should_cleanup else '✗'})")
    
    # 清理测试文件
    if os.path.exists(monitor.log_cleanup_flag):
        os.remove(monitor.log_cleanup_flag)
    os.remove('test-timing.conf')
    print(f"\n🗑️ 清理测试文件")

def main():
    """主函数"""
    try:
        test_log_cleanup()
        test_cleanup_timing()
        
        print("\n=== 测试总结 ===")
        print("✅ 日志内容清理功能正常")
        print("✅ 保留新日志，删除旧日志")
        print("✅ 保留无时间戳的日志行")
        print("✅ 添加清理记录到日志")
        print("✅ 清理时机控制正常")
        print("\n🎉 日志清理功能测试完成！")
        
        print("\n📝 功能说明:")
        print("• 每7天自动清理一次日志内容")
        print("• 只删除7天前的日志行，保留最新日志")
        print("• 保留没有时间戳的日志行")
        print("• 在日志中记录清理操作")
        print("• 脚本启动时和每100次循环检查一次")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
