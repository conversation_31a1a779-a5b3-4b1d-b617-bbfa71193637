#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java服务监控和自动重启脚本 (Python版本)
使用方法: python monitor-services.py [配置文件路径]
"""

import os
import sys
import time
import json
import signal
import logging
import subprocess
import configparser
from datetime import datetime, timedelta
from pathlib import Path
import requests
import psutil


class ServiceMonitor:
    def __init__(self, config_file="./service-monitor.conf"):
        self.config_file = config_file
        # 固定日志文件路径
        self.log_file = "/opt/monitor/logs/service-monitor.log"
        self.log_cleanup_flag = "/opt/monitor/logs/.log_cleanup_flag"

        # 确保日志目录存在
        os.makedirs("/opt/monitor/logs", exist_ok=True)
        self.running = True
        self.loop_count = 0

        # 告警计数器 - 记录每个服务的告警次数
        self.alert_counters = {}
        # 告警静默状态 - 记录哪些服务已经超过最大告警次数
        self.alert_silenced = {}

        # 设置日志
        self.setup_logging()

        # 加载配置
        self.load_config()

        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

    def _determine_log_path(self):
        """确定日志文件路径，按优先级选择可写位置"""
        # 优先级1: /opt/monitor/logs/service-monitor.log (推荐部署位置)
        opt_log_path = "/opt/monitor/logs/service-monitor.log"
        if self._can_write_to_path(opt_log_path):
            return opt_log_path

        # 优先级2: /var/log/service-monitor.log (传统系统日志位置)
        var_log_path = "/var/log/service-monitor.log"
        if self._can_write_to_path(var_log_path):
            return var_log_path

        # 优先级3: ./service-monitor.log (当前目录，兜底方案)
        return "./service-monitor.log"

    def _can_write_to_path(self, file_path):
        """检查是否可以写入指定路径"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            # 尝试创建或触碰文件
            Path(file_path).touch()
            return True
        except (PermissionError, OSError):
            return False

    def setup_logging(self):
        """设置日志配置"""
        # 确保日志文件存在
        Path(self.log_file).touch()

        # 配置日志格式
        logging.basicConfig(
            level=logging.INFO,
            format='[%(asctime)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S',
            handlers=[
                logging.FileHandler(self.log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)

    def load_config(self):
        """加载配置文件"""
        if not os.path.exists(self.config_file):
            self.logger.error(f"错误: 配置文件 {self.config_file} 不存在")
            sys.exit(1)

        # 解析bash风格的配置文件
        self.config = {}
        with open(self.config_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    # 移除引号
                    value = value.strip('"\'')
                    self.config[key] = value

        # 检查必要配置
        if 'SERVICES' not in self.config:
            self.logger.error("错误: 配置文件中未定义服务列表 (SERVICES)")
            sys.exit(1)

        # 解析服务列表
        self.services = self.config['SERVICES'].split()
        self.check_interval = int(self.config.get('CHECK_INTERVAL', 60))
        self.ding_api_url = self.config.get('DING_API_URL', '')
        # 最大告警次数配置，默认为5次
        self.max_alert_count = int(self.config.get('MAX_ALERT_COUNT', 5))

        self.logger.info(f"加载配置完成，监控服务: {', '.join(self.services)}")
        self.logger.info(f"最大告警次数: {self.max_alert_count}次")

    def send_ding_notification(self, content):
        """发送钉钉通知"""
        if not self.ding_api_url:
            self.logger.warning("钉钉API URL未配置，跳过通知")
            return

        message_body = {
            "msgtype": "markdown",
            "markdown": {
                "title": "服务监控提醒",
                "text": f"### 服务监控提醒 \n  * {content}"
            }
        }

        try:
            response = requests.post(
                self.ding_api_url,
                json=message_body,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )

            if response.status_code == 200:
                self.logger.info(f"钉钉通知发送成功: {content}")
            else:
                self.logger.error(f"钉钉通知发送失败: {content}, 状态码: {response.status_code}")
        except Exception as e:
            self.logger.error(f"钉钉通知发送异常: {content}, 错误: {str(e)}")

    def increment_alert_counter(self, service_name):
        """增加服务告警计数"""
        if service_name not in self.alert_counters:
            self.alert_counters[service_name] = 0
        self.alert_counters[service_name] += 1
        return self.alert_counters[service_name]

    def reset_alert_counter(self, service_name):
        """重置服务告警计数"""
        if service_name in self.alert_counters:
            self.alert_counters[service_name] = 0
        if service_name in self.alert_silenced:
            del self.alert_silenced[service_name]
        self.logger.info(f"服务 {service_name} 告警计数已重置")

    def is_alert_silenced(self, service_name):
        """检查服务是否已被静默"""
        return self.alert_silenced.get(service_name, False)

    def silence_alert(self, service_name):
        """静默服务告警"""
        self.alert_silenced[service_name] = True
        self.logger.warning(f"服务 {service_name} 已达到最大告警次数({self.max_alert_count}次)，后续告警将被静默")

    def send_alert_with_limit(self, service_name, content):
        """发送带限制的告警通知"""
        # 如果服务已被静默，不发送告警
        if self.is_alert_silenced(service_name):
            self.logger.debug(f"服务 {service_name} 告警已被静默，跳过通知: {content}")
            return False

        # 增加告警计数
        current_count = self.increment_alert_counter(service_name)

        # 在告警内容中添加计数信息
        content_with_count = f"{content} (第{current_count}次告警)"

        # 发送告警
        self.send_ding_notification(content_with_count)

        # 检查是否达到最大告警次数
        if current_count >= self.max_alert_count:
            self.silence_alert(service_name)
            # 发送静默通知
            silence_msg = f"【告警静默】服务 {service_name} 已连续告警{self.max_alert_count}次，后续告警将被静默，请及时处理！"
            self.send_ding_notification(silence_msg)
            return False

        return True

    def cleanup_logs(self):
        """日志清理函数 - 清理7天前的日志内容，保留最新日志"""
        current_time = time.time()
        cleanup_flag_time = 0

        # 检查上次清理时间
        if os.path.exists(self.log_cleanup_flag):
            try:
                with open(self.log_cleanup_flag, 'r') as f:
                    cleanup_flag_time = float(f.read().strip())
            except:
                cleanup_flag_time = 0

        # 计算时间差（秒）
        time_diff = current_time - cleanup_flag_time
        seven_days_seconds = 7 * 24 * 3600

        # 如果距离上次清理超过7天，则执行清理
        if time_diff > seven_days_seconds:
            self.logger.info("开始执行日志清理任务，清理7天前的日志内容...")

            cleaned_lines = 0
            total_files_processed = 0

            # 定义service-monitor.log的可能位置（按优先级排序）
            monitor_log_paths = [
                "/opt/monitor/logs/service-monitor.log",  # 推荐部署位置
                "/var/log/service-monitor.log",           # 传统系统日志位置
                "./service-monitor.log",                  # 当前目录
                "../service-monitor.log",                 # 上级目录
                "/tmp/service-monitor.log"                # 临时目录
            ]

            # 清理日志文件内容（保留7天内的日志）
            for log_path in monitor_log_paths:
                if os.path.exists(log_path):
                    try:
                        total_files_processed += 1
                        lines_cleaned = self._clean_log_file_content(log_path, seven_days_seconds)
                        cleaned_lines += lines_cleaned
                        if lines_cleaned > 0:
                            self.logger.info(f"清理日志文件 {log_path}，删除了 {lines_cleaned} 行旧日志")
                    except Exception as e:
                        self.logger.error(f"清理日志文件失败 {log_path}: {str(e)}")

            # 更新清理标记文件
            with open(self.log_cleanup_flag, 'w') as f:
                f.write(str(current_time))

            self.logger.info(f"日志清理任务完成，处理文件数: {total_files_processed}，清理日志行数: {cleaned_lines}")
            if cleaned_lines > 0:
                self.send_ding_notification(f"【日志清理】已清理7天前的监控日志，删除 {cleaned_lines} 行旧日志，处理 {total_files_processed} 个文件")

    def _clean_log_file_content(self, log_path, seven_days_seconds):
        """清理日志文件内容，保留7天内的日志"""
        try:
            current_time = time.time()
            cutoff_time = current_time - seven_days_seconds

            # 读取所有日志行
            with open(log_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 过滤保留7天内的日志
            kept_lines = []
            cleaned_count = 0

            for line in lines:
                # 尝试解析日志时间戳 [2025-07-02 10:25:08]
                if line.startswith('[') and ']' in line:
                    try:
                        timestamp_str = line[1:line.index(']')]
                        log_time = time.mktime(time.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S'))

                        if log_time >= cutoff_time:
                            kept_lines.append(line)
                        else:
                            cleaned_count += 1
                    except:
                        # 如果时间戳解析失败，保留该行
                        kept_lines.append(line)
                else:
                    # 没有时间戳的行（可能是多行日志的续行），保留
                    kept_lines.append(line)

            # 如果有日志被清理，重写文件
            if cleaned_count > 0:
                with open(log_path, 'w', encoding='utf-8') as f:
                    f.writelines(kept_lines)

                # 添加清理记录
                cleanup_msg = f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] 日志清理：删除了 {cleaned_count} 行7天前的日志\n"
                with open(log_path, 'a', encoding='utf-8') as f:
                    f.write(cleanup_msg)

            return cleaned_count

        except Exception as e:
            self.logger.error(f"清理日志文件内容失败 {log_path}: {str(e)}")
            return 0

    def is_service_running(self, process_keyword):
        """检查服务是否运行"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if process_keyword in cmdline:
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            return False
        except Exception as e:
            self.logger.error(f"检查进程时发生错误: {str(e)}")
            return False

    def restart_service(self, service_name, jar_path, java_opts, process_keyword):
        """重启服务"""
        self.logger.warning(f"警告: 服务 {service_name} 未运行，正在重启...")

        # 发送带限制的服务停止通知
        self.send_alert_with_limit(service_name, f"【服务告警】服务 {service_name} 已停止运行，正在尝试自动重启...")

        # 获取jar文件所在目录
        jar_dir = os.path.dirname(jar_path)

        try:
            # 构建启动命令
            if java_opts:
                cmd = f"nohup java {java_opts} -jar {jar_path} --spring.profiles.active=prod >/dev/null 2>&1 &"
            else:
                cmd = f"nohup java -jar {jar_path} --spring.profiles.active=prod >/dev/null 2>&1 &"

            # 在jar目录中执行命令
            subprocess.Popen(cmd, shell=True, cwd=jar_dir)

            # 等待服务启动
            time.sleep(15)

            # 检查启动是否成功
            if self.is_service_running(process_keyword):
                self.logger.info(f"服务 {service_name} 已成功重启")
                # 服务恢复时重置告警计数
                self.reset_alert_counter(service_name)
                self.send_ding_notification(f"【服务恢复】服务 {service_name} 已成功自动重启并正常运行")
            else:
                self.logger.error(f"错误: 服务 {service_name} 重启失败")
                self.send_alert_with_limit(service_name, f"【服务告警】服务 {service_name} 重启失败，请立即手动检查！")

        except Exception as e:
            self.logger.error(f"重启服务 {service_name} 时发生异常: {str(e)}")
            self.send_alert_with_limit(service_name, f"【服务告警】服务 {service_name} 重启异常：{str(e)}")

    def monitor_and_restart(self, service_name, jar_path, java_opts, process_keyword):
        """监控并重启服务"""
        # 检查服务是否运行
        if self.is_service_running(process_keyword):
            self.logger.info(f"服务 {service_name} 正在运行")
            # 如果服务正常运行且之前有告警计数，则重置计数
            if service_name in self.alert_counters and self.alert_counters[service_name] > 0:
                self.reset_alert_counter(service_name)
        else:
            self.restart_service(service_name, jar_path, java_opts, process_keyword)

    def get_service_config(self, service):
        """获取服务配置"""
        service_name = self.config.get(f"{service}_NAME", "")
        jar_path = self.config.get(f"{service}_JAR_PATH", "")
        java_opts = self.config.get(f"{service}_JAVA_OPTS", "")
        process_keyword = self.config.get(f"{service}_PROCESS_KEYWORD", "")

        return service_name, jar_path, java_opts, process_keyword

    def signal_handler(self, signum, frame):
        """信号处理函数"""
        self.logger.info("服务监控脚本正在退出...")
        self.send_ding_notification("【系统通知】服务监控脚本已停止运行")
        self.running = False

    def initialize_script(self):
        """脚本初始化"""
        self.logger.info("服务监控脚本已启动")
        self.send_ding_notification("【系统通知】服务监控脚本已启动，开始监控服务状态")
        self.cleanup_logs()

    def run(self):
        """主运行循环"""
        self.initialize_script()

        while self.running:
            try:
                # 每100次循环检查一次日志清理（约每小时检查一次，假设CHECK_INTERVAL为60秒）
                if self.loop_count % 100 == 0:
                    self.cleanup_logs()

                for service in self.services:
                    # 获取服务配置
                    service_name, jar_path, java_opts, process_keyword = self.get_service_config(service)

                    # 检查配置是否完整
                    if not service_name or not jar_path or not process_keyword:
                        self.logger.error(f"错误: 服务 {service} 配置不完整")
                        continue

                    # 监控并在需要时重启服务
                    self.monitor_and_restart(service_name, jar_path, java_opts, process_keyword)

                # 增加循环计数器
                self.loop_count += 1

                # 等待下一次检查
                time.sleep(self.check_interval)

            except Exception as e:
                self.logger.error(f"监控循环中发生异常: {str(e)}")
                time.sleep(self.check_interval)


def show_help():
    """显示帮助信息"""
    help_text = """
服务监控脚本 - Python版本

用法:
    python3 monitor-services.py [配置文件]

参数:
    配置文件    可选，指定配置文件路径，默认为 ./service-monitor.conf

选项:
    -h, --help    显示此帮助信息
    -v, --version 显示版本信息

功能特性:
    • 自动监控Java服务进程
    • 服务故障时自动重启
    • 钉钉通知集成
    • 告警限制功能（防止告警轰炸）
    • 多服务独立监控
    • 详细的日志记录

配置示例:
    CHECK_INTERVAL=60
    MAX_ALERT_COUNT=5
    DING_API_URL=https://oapi.dingtalk.com/robot/send?access_token=your_token
    SERVICES=GATEWAY AUTH SYSTEM

更多信息请查看配置文件示例和文档。
"""
    print(help_text)

def show_version():
    """显示版本信息"""
    print("服务监控脚本 v2.0")
    print("新增功能: 告警限制功能")
    print("作者: Python服务监控系统")

def main():
    """主函数"""
    # 处理命令行参数
    if len(sys.argv) > 1:
        arg = sys.argv[1]
        if arg in ['-h', '--help']:
            show_help()
            return
        elif arg in ['-v', '--version']:
            show_version()
            return
        else:
            config_file = arg
    else:
        config_file = "./service-monitor.conf"

    try:
        print(f"启动服务监控，配置文件: {config_file}")
        monitor = ServiceMonitor(config_file)
        monitor.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行异常: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()