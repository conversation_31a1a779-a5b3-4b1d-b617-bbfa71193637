#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java服务监控和自动重启脚本 (Python版本)
使用方法: python monitor-services.py [配置文件路径]
"""

import os
import sys
import time
import json
import signal
import logging
import subprocess
import configparser
from datetime import datetime, timedelta
from pathlib import Path
import requests
import psutil


class ServiceMonitor:
    def __init__(self, config_file="./service-monitor.conf"):
        self.config_file = config_file
        # 如果无法写入/var/log，则使用当前目录
        try:
            Path("/var/log/service-monitor.log").touch()
            self.log_file = "/var/log/service-monitor.log"
            self.log_cleanup_flag = "/var/log/.log_cleanup_flag"
        except PermissionError:
            self.log_file = "./service-monitor.log"
            self.log_cleanup_flag = "./.log_cleanup_flag"
        self.running = True
        self.loop_count = 0

        # 告警计数器 - 记录每个服务的告警次数
        self.alert_counters = {}
        # 告警静默状态 - 记录哪些服务已经超过最大告警次数
        self.alert_silenced = {}

        # 设置日志
        self.setup_logging()

        # 加载配置
        self.load_config()

        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

    def setup_logging(self):
        """设置日志配置"""
        # 确保日志文件存在
        Path(self.log_file).touch()

        # 配置日志格式
        logging.basicConfig(
            level=logging.INFO,
            format='[%(asctime)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S',
            handlers=[
                logging.FileHandler(self.log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)

    def load_config(self):
        """加载配置文件"""
        if not os.path.exists(self.config_file):
            self.logger.error(f"错误: 配置文件 {self.config_file} 不存在")
            sys.exit(1)

        # 解析bash风格的配置文件
        self.config = {}
        with open(self.config_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    # 移除引号
                    value = value.strip('"\'')
                    self.config[key] = value

        # 检查必要配置
        if 'SERVICES' not in self.config:
            self.logger.error("错误: 配置文件中未定义服务列表 (SERVICES)")
            sys.exit(1)

        # 解析服务列表
        self.services = self.config['SERVICES'].split()
        self.check_interval = int(self.config.get('CHECK_INTERVAL', 60))
        self.ding_api_url = self.config.get('DING_API_URL', '')

        self.logger.info(f"加载配置完成，监控服务: {', '.join(self.services)}")

    def send_ding_notification(self, content):
        """发送钉钉通知"""
        if not self.ding_api_url:
            self.logger.warning("钉钉API URL未配置，跳过通知")
            return

        message_body = {
            "msgtype": "markdown",
            "markdown": {
                "title": "服务监控提醒",
                "text": f"### 服务监控提醒 \n  * {content}"
            }
        }

        try:
            response = requests.post(
                self.ding_api_url,
                json=message_body,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )

            if response.status_code == 200:
                self.logger.info(f"钉钉通知发送成功: {content}")
            else:
                self.logger.error(f"钉钉通知发送失败: {content}, 状态码: {response.status_code}")
        except Exception as e:
            self.logger.error(f"钉钉通知发送异常: {content}, 错误: {str(e)}")

    def cleanup_logs(self):
        """日志清理函数"""
        current_time = time.time()
        cleanup_flag_time = 0

        # 检查上次清理时间
        if os.path.exists(self.log_cleanup_flag):
            try:
                with open(self.log_cleanup_flag, 'r') as f:
                    cleanup_flag_time = float(f.read().strip())
            except:
                cleanup_flag_time = 0

        # 计算时间差（秒）
        time_diff = current_time - cleanup_flag_time
        seven_days_seconds = 7 * 24 * 3600

        # 如果距离上次清理超过7天，则执行清理
        if time_diff > seven_days_seconds:
            self.logger.info("开始执行service-monitor.log日志清理任务...")

            cleaned_files = 0

            # 定义service-monitor.log的可能位置
            monitor_log_paths = [
                "/var/log/service-monitor.log",
                "./service-monitor.log",
                "../service-monitor.log",
                "/tmp/service-monitor.log"
            ]

            # 清理service-monitor.log文件（7天前的）
            for log_path in monitor_log_paths:
                if os.path.exists(log_path):
                    try:
                        # 检查文件修改时间
                        file_mtime = os.path.getmtime(log_path)
                        if current_time - file_mtime > seven_days_seconds:
                            os.remove(log_path)
                            cleaned_files += 1
                            self.logger.info(f"清理service-monitor.log: {log_path}")
                    except Exception as e:
                        self.logger.error(f"清理日志文件失败 {log_path}: {str(e)}")

            # 更新清理标记文件
            with open(self.log_cleanup_flag, 'w') as f:
                f.write(str(current_time))

            self.logger.info(f"service-monitor.log日志清理任务完成，清理文件数量: {cleaned_files}")
            self.send_ding_notification(f"service-monitor.log日志清理完成，已清理7天前的监控日志文件，清理数量: {cleaned_files}个")

    def is_service_running(self, process_keyword):
        """检查服务是否运行"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if process_keyword in cmdline:
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            return False
        except Exception as e:
            self.logger.error(f"检查进程时发生错误: {str(e)}")
            return False

    def restart_service(self, service_name, jar_path, java_opts, process_keyword):
        """重启服务"""
        self.logger.warning(f"警告: 服务 {service_name} 未运行，正在重启...")

        # 发送服务停止通知
        self.send_ding_notification(f"【服务告警】服务 {service_name} 已停止运行，正在尝试自动重启...")

        # 获取jar文件所在目录
        jar_dir = os.path.dirname(jar_path)

        try:
            # 构建启动命令
            if java_opts:
                cmd = f"nohup java {java_opts} -jar {jar_path} --spring.profiles.active=prod >/dev/null 2>&1 &"
            else:
                cmd = f"nohup java -jar {jar_path} --spring.profiles.active=prod >/dev/null 2>&1 &"

            # 在jar目录中执行命令
            subprocess.Popen(cmd, shell=True, cwd=jar_dir)

            # 等待服务启动
            time.sleep(15)

            # 检查启动是否成功
            if self.is_service_running(process_keyword):
                self.logger.info(f"服务 {service_name} 已成功重启")
                self.send_ding_notification(f"【服务恢复】服务 {service_name} 已成功自动重启并正常运行")
            else:
                self.logger.error(f"错误: 服务 {service_name} 重启失败")
                self.send_ding_notification(f"【服务告警】服务 {service_name} 重启失败，请立即手动检查！")

        except Exception as e:
            self.logger.error(f"重启服务 {service_name} 时发生异常: {str(e)}")
            self.send_ding_notification(f"【服务告警】服务 {service_name} 重启异常：{str(e)}")

    def monitor_and_restart(self, service_name, jar_path, java_opts, process_keyword):
        """监控并重启服务"""
        # 检查服务是否运行
        if self.is_service_running(process_keyword):
            self.logger.info(f"服务 {service_name} 正在运行")
        else:
            self.restart_service(service_name, jar_path, java_opts, process_keyword)

    def get_service_config(self, service):
        """获取服务配置"""
        service_name = self.config.get(f"{service}_NAME", "")
        jar_path = self.config.get(f"{service}_JAR_PATH", "")
        java_opts = self.config.get(f"{service}_JAVA_OPTS", "")
        process_keyword = self.config.get(f"{service}_PROCESS_KEYWORD", "")

        return service_name, jar_path, java_opts, process_keyword

    def signal_handler(self, signum, frame):
        """信号处理函数"""
        self.logger.info("服务监控脚本正在退出...")
        self.send_ding_notification("【系统通知】服务监控脚本已停止运行")
        self.running = False

    def initialize_script(self):
        """脚本初始化"""
        self.logger.info("服务监控脚本已启动")
        self.send_ding_notification("【系统通知】服务监控脚本已启动，开始监控服务状态")
        self.cleanup_logs()

    def run(self):
        """主运行循环"""
        self.initialize_script()

        while self.running:
            try:
                # 每100次循环检查一次日志清理（约每小时检查一次，假设CHECK_INTERVAL为60秒）
                if self.loop_count % 100 == 0:
                    self.cleanup_logs()

                for service in self.services:
                    # 获取服务配置
                    service_name, jar_path, java_opts, process_keyword = self.get_service_config(service)

                    # 检查配置是否完整
                    if not service_name or not jar_path or not process_keyword:
                        self.logger.error(f"错误: 服务 {service} 配置不完整")
                        continue

                    # 监控并在需要时重启服务
                    self.monitor_and_restart(service_name, jar_path, java_opts, process_keyword)

                # 增加循环计数器
                self.loop_count += 1

                # 等待下一次检查
                time.sleep(self.check_interval)

            except Exception as e:
                self.logger.error(f"监控循环中发生异常: {str(e)}")
                time.sleep(self.check_interval)


def main():
    """主函数"""
    config_file = sys.argv[1] if len(sys.argv) > 1 else "./service-monitor.conf"

    try:
        monitor = ServiceMonitor(config_file)
        monitor.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行异常: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()