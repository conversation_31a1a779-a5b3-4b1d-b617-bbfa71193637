#!/usr/bin/env python3
"""
测试告警限制功能
"""

import sys
import time
import importlib.util

# 动态导入monitor-services.py
spec = importlib.util.spec_from_file_location("monitor_services", "monitor-services.py")
monitor_services = importlib.util.module_from_spec(spec)
spec.loader.exec_module(monitor_services)

def create_test_config():
    """创建测试配置文件"""
    config_content = """# 告警限制测试配置文件
CHECK_INTERVAL=5
MAX_ALERT_COUNT=3
DING_API_URL=
SERVICES=TEST

# 测试服务配置
TEST_NAME=测试告警限制服务
TEST_JAR_PATH=/tmp/test-alert.jar
TEST_JAVA_OPTS=
TEST_PROCESS_KEYWORD=test-alert-nonexistent-process
"""
    
    with open('test-alert-config.conf', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✓ 创建测试配置文件: test-alert-config.conf")

def test_alert_limit():
    """测试告警限制功能"""
    print("=== 告警限制功能测试 ===\n")
    
    # 创建测试配置
    create_test_config()
    
    # 初始化监控器
    print("1. 初始化监控器...")
    monitor = monitor_services.ServiceMonitor('test-alert-config.conf')
    print(f"   ✓ 最大告警次数: {monitor.max_alert_count}")
    
    # 获取服务配置
    service_name, jar_path, java_opts, process_keyword = monitor.get_service_config('TEST')
    print(f"   ✓ 测试服务: {service_name}")
    
    print("\n2. 模拟连续告警...")
    
    # 模拟多次服务故障，测试告警限制
    for i in range(8):  # 测试8次，超过最大告警次数3次
        print(f"\n   第{i+1}次检查:")
        
        # 检查服务状态（肯定是未运行的）
        is_running = monitor.is_service_running(process_keyword)
        print(f"   - 服务状态: {'运行中' if is_running else '未运行'}")
        
        if not is_running:
            # 获取当前告警计数
            current_count = monitor.alert_counters.get(service_name, 0)
            is_silenced = monitor.is_alert_silenced(service_name)
            
            print(f"   - 当前告警计数: {current_count}")
            print(f"   - 是否已静默: {is_silenced}")
            
            # 模拟发送告警（不实际重启服务）
            if not is_silenced:
                alert_sent = monitor.send_alert_with_limit(
                    service_name, 
                    f"【测试告警】服务 {service_name} 模拟故障"
                )
                print(f"   - 告警发送结果: {'成功' if alert_sent else '已静默'}")
            else:
                print(f"   - 告警已被静默，跳过发送")
        
        # 显示当前状态
        final_count = monitor.alert_counters.get(service_name, 0)
        final_silenced = monitor.is_alert_silenced(service_name)
        print(f"   - 最终告警计数: {final_count}")
        print(f"   - 最终静默状态: {final_silenced}")
        
        # 等待一下再进行下次测试
        if i < 7:
            time.sleep(1)
    
    print("\n3. 测试服务恢复...")
    
    # 模拟服务恢复，测试告警计数重置
    print("   模拟服务恢复，重置告警计数...")
    monitor.reset_alert_counter(service_name)
    
    final_count = monitor.alert_counters.get(service_name, 0)
    final_silenced = monitor.is_alert_silenced(service_name)
    print(f"   ✓ 重置后告警计数: {final_count}")
    print(f"   ✓ 重置后静默状态: {final_silenced}")
    
    print("\n4. 测试重置后的告警...")
    
    # 重置后再次测试告警
    alert_sent = monitor.send_alert_with_limit(
        service_name, 
        f"【测试告警】服务 {service_name} 重置后的新告警"
    )
    print(f"   ✓ 重置后告警发送结果: {'成功' if alert_sent else '失败'}")
    
    final_count = monitor.alert_counters.get(service_name, 0)
    print(f"   ✓ 新告警后计数: {final_count}")

def test_multiple_services():
    """测试多服务告警限制"""
    print("\n=== 多服务告警限制测试 ===\n")
    
    # 创建多服务配置
    config_content = """# 多服务告警限制测试配置
CHECK_INTERVAL=5
MAX_ALERT_COUNT=2
DING_API_URL=
SERVICES=SERVICE1 SERVICE2

# 服务1配置
SERVICE1_NAME=测试服务1
SERVICE1_JAR_PATH=/tmp/service1.jar
SERVICE1_JAVA_OPTS=
SERVICE1_PROCESS_KEYWORD=service1-nonexistent

# 服务2配置
SERVICE2_NAME=测试服务2
SERVICE2_JAR_PATH=/tmp/service2.jar
SERVICE2_JAVA_OPTS=
SERVICE2_PROCESS_KEYWORD=service2-nonexistent
"""
    
    with open('test-multi-config.conf', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✓ 创建多服务测试配置")
    
    # 初始化监控器
    monitor = monitor_services.ServiceMonitor('test-multi-config.conf')
    
    # 测试两个服务的独立告警计数
    services = [
        ('测试服务1', 'service1-nonexistent'),
        ('测试服务2', 'service2-nonexistent')
    ]
    
    for service_name, process_keyword in services:
        print(f"\n测试服务: {service_name}")
        
        # 发送3次告警，超过限制(2次)
        for i in range(3):
            alert_sent = monitor.send_alert_with_limit(
                service_name, 
                f"【测试告警】{service_name} 第{i+1}次故障"
            )
            count = monitor.alert_counters.get(service_name, 0)
            silenced = monitor.is_alert_silenced(service_name)
            print(f"  第{i+1}次告警: 发送{'成功' if alert_sent else '失败'}, 计数:{count}, 静默:{silenced}")

def main():
    """主函数"""
    try:
        test_alert_limit()
        test_multiple_services()
        
        print("\n=== 测试总结 ===")
        print("✓ 告警计数功能正常")
        print("✓ 告警限制功能正常")
        print("✓ 告警静默功能正常")
        print("✓ 告警重置功能正常")
        print("✓ 多服务独立计数正常")
        print("\n告警限制功能测试完成！")
        
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
