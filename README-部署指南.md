# Python服务监控系统 - 快速部署指南

## 🚀 一键部署

### 1. 自动部署（推荐）

```bash
# 下载并运行一键部署脚本
sudo ./deploy-monitor.sh
```

### 2. 配置向导

```bash
# 运行配置向导，快速配置监控服务
sudo ./config-wizard.sh
```

### 3. 验证部署

```bash
# 查看服务状态
sudo systemctl status monitor-services

# 查看实时日志
sudo journalctl -u monitor-services -f
```

## 📁 部署文件说明

| 文件名 | 说明 |
|--------|------|
| `monitor-services.py` | 主监控脚本 |
| `deploy-monitor.sh` | 一键部署脚本 |
| `config-wizard.sh` | 配置向导脚本 |
| `monitor-services.service` | systemd服务模板 |
| `部署说明文档.md` | 详细部署文档 |
| `monitor-services使用说明.md` | 完整使用说明 |

## ⚡ 快速开始

### 最简部署流程

```bash
# 1. 一键部署
sudo ./deploy-monitor.sh

# 2. 配置服务
sudo ./config-wizard.sh

# 3. 查看状态
sudo systemctl status monitor-services
```

### 手动部署流程

```bash
# 1. 创建用户和目录
sudo useradd -r -s /bin/false monitor
sudo mkdir -p /opt/monitor/{logs,backup}
sudo chown -R monitor:monitor /opt/monitor

# 2. 复制文件
sudo cp monitor-services.py /opt/monitor/
sudo cp service-monitor-python.conf /opt/monitor/monitor.conf
sudo chown monitor:monitor /opt/monitor/*

# 3. 安装依赖
sudo pip3 install requests psutil

# 4. 创建systemd服务
sudo cp monitor-services.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable monitor-services
sudo systemctl start monitor-services
```

## 🔧 配置说明

### 基础配置

编辑 `/opt/monitor/monitor.conf`：

```bash
# 基本设置
CHECK_INTERVAL=60                # 检查间隔（秒）
MAX_ALERT_COUNT=5               # 最大告警次数
DING_API_URL=your_webhook_url   # 钉钉通知URL

# 监控服务
SERVICES=GATEWAY AUTH SYSTEM

# 服务配置示例
GATEWAY_NAME=API网关服务
GATEWAY_JAR_PATH=/app/gateway/gateway.jar
GATEWAY_JAVA_OPTS=-Xms1g -Xmx2g
GATEWAY_PROCESS_KEYWORD=gateway.jar
```

### 重新加载配置

```bash
sudo systemctl reload monitor-services
```

## 📊 监控管理

### 常用命令

```bash
# 服务管理
sudo systemctl start monitor-services     # 启动
sudo systemctl stop monitor-services      # 停止
sudo systemctl restart monitor-services   # 重启
sudo systemctl status monitor-services    # 状态

# 日志查看
sudo journalctl -u monitor-services -f    # 系统日志
sudo tail -f /opt/monitor/logs/service-monitor.log  # 应用日志

# 配置管理
sudo vim /opt/monitor/monitor.conf         # 编辑配置
sudo systemctl reload monitor-services     # 重新加载
```

### 功能特性

- ✅ **自动监控**：定期检查Java服务状态
- ✅ **自动重启**：服务异常时自动重启
- ✅ **告警限制**：防止告警轰炸（每服务最多5次）
- ✅ **钉钉通知**：支持钉钉机器人通知
- ✅ **日志管理**：7天自动清理旧日志
- ✅ **开机自启**：系统重启后自动启动监控

## 🛠️ 故障排除

### 常见问题

1. **服务启动失败**
```bash
# 检查配置文件
sudo -u monitor python3 /opt/monitor/monitor-services.py /opt/monitor/monitor.conf --help

# 查看详细错误
sudo journalctl -u monitor-services -n 20
```

2. **权限问题**
```bash
# 修复权限
sudo chown -R monitor:monitor /opt/monitor
sudo chmod 755 /opt/monitor/monitor-services.py
```

3. **依赖问题**
```bash
# 重新安装依赖
sudo pip3 install requests psutil
```

### 调试模式

```bash
# 前台运行调试
sudo -u monitor python3 /opt/monitor/monitor-services.py /opt/monitor/monitor.conf
```

## 📈 高级功能

### 多环境部署

```bash
# 开发环境
sudo ./deploy-monitor.sh
sudo cp /opt/monitor/monitor.conf /opt/monitor/monitor-dev.conf

# 生产环境
sudo ./deploy-monitor.sh
sudo cp /opt/monitor/monitor.conf /opt/monitor/monitor-prod.conf
```

### 监控指标

```bash
# 查看监控统计
grep "监控循环" /opt/monitor/logs/service-monitor.log | tail -5
grep "告警" /opt/monitor/logs/service-monitor.log | wc -l
grep "重启" /opt/monitor/logs/service-monitor.log | wc -l
```

### 定期维护

```bash
# 运行维护脚本（部署后自动创建）
sudo /opt/monitor/scripts/maintenance.sh

# 设置定时维护
sudo crontab -e
# 添加：0 2 * * * /opt/monitor/scripts/maintenance.sh
```

## 📞 技术支持

### 获取帮助

```bash
# 查看帮助信息
python3 monitor-services.py --help

# 运行测试脚本
python3 test_monitor.py
python3 test_alert_limit.py
```

### 文档资源

- `部署说明文档.md` - 详细部署说明
- `monitor-services使用说明.md` - 完整功能说明
- `日志清理功能检查报告.md` - 日志清理功能说明

## 🎯 部署检查清单

- [ ] 运行一键部署脚本
- [ ] 配置监控服务列表
- [ ] 设置钉钉通知URL
- [ ] 验证服务启动成功
- [ ] 测试监控功能
- [ ] 检查日志输出
- [ ] 确认开机自启

---

**部署完成后，监控系统将自动运行，7×24小时监控您的Java服务，确保服务稳定运行！**
