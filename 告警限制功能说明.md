# 告警限制功能说明

## 功能概述

告警限制功能是为了防止服务监控系统在服务持续故障时产生告警轰炸而设计的。当某个服务连续告警达到设定的最大次数后，系统会自动静默该服务的后续告警，直到服务恢复正常。

## 主要特性

### 🛡️ 防止告警轰炸
- 每个服务设置最大连续告警次数
- 超过限制后自动静默告警
- 避免过度通知，减少告警疲劳

### 🔄 自动管理
- 服务恢复时自动重置告警计数
- 无需人工干预，全自动管理
- 智能识别服务状态变化

### 🎯 精确控制
- 多服务独立计数，互不影响
- 每个服务都有独立的告警状态
- 支持不同服务设置不同的告警限制

### 📊 状态跟踪
- 实时记录每个服务的告警次数
- 详细的日志记录告警状态变化
- 清晰的告警计数显示

## 配置方法

### 1. 基本配置

在配置文件中添加以下参数：

```bash
# 最大告警次数（默认5次）
MAX_ALERT_COUNT=5
```

### 2. 配置示例

```bash
# 服务监控配置文件
CHECK_INTERVAL=60
MAX_ALERT_COUNT=5
DING_API_URL=https://oapi.dingtalk.com/robot/send?access_token=your_token

# 服务列表
SERVICES=GATEWAY AUTH SYSTEM

# 服务配置
GATEWAY_NAME=网关服务
GATEWAY_JAR_PATH=/app/gateway.jar
GATEWAY_JAVA_OPTS=-Xms512m -Xmx1024m
GATEWAY_PROCESS_KEYWORD=gateway.jar
```

## 工作流程

### 告警流程
```
服务故障 → 发送告警 → 计数+1 → 检查是否达到上限
    ↓
达到上限 → 发送静默通知 → 静默后续告警
    ↓
继续故障 → 跳过告警发送 → 记录日志
```

### 恢复流程
```
服务恢复 → 检测到服务正常 → 重置告警计数 → 清除静默状态
    ↓
后续故障 → 重新开始告警计数 → 正常发送告警
```

## 使用示例

### 场景1：连续告警
```
第1次故障 → 【服务告警】服务 GATEWAY 已停止运行 (第1次告警)
第2次故障 → 【服务告警】服务 GATEWAY 已停止运行 (第2次告警)
...
第5次故障 → 【服务告警】服务 GATEWAY 已停止运行 (第5次告警)
             【告警静默】服务 GATEWAY 已连续告警5次，后续告警将被静默
第6次故障 → (不发送告警，记录日志)
```

### 场景2：服务恢复
```
服务恢复 → 【服务恢复】服务 GATEWAY 已成功自动重启并正常运行
          (自动重置告警计数为0，清除静默状态)
```

### 场景3：多服务独立
```
服务A故障5次 → 服务A被静默
服务B故障3次 → 服务B继续正常告警 (独立计数)
```

## 日志示例

```
[2025-07-02 10:25:08] 服务 GATEWAY 告警计数: 1
[2025-07-02 10:25:09] 服务 GATEWAY 告警计数: 2
[2025-07-02 10:25:10] 服务 GATEWAY 已达到最大告警次数(5次)，后续告警将被静默
[2025-07-02 10:25:11] 服务 GATEWAY 告警已被静默，跳过通知
[2025-07-02 10:26:00] 服务 GATEWAY 告警计数已重置
```

## 测试验证

### 运行测试脚本
```bash
# 功能测试
python3 test_alert_limit.py

# 演示脚本
python3 demo_alert_limit.py
```

### 测试内容
- ✅ 告警计数功能
- ✅ 告警限制功能
- ✅ 告警静默功能
- ✅ 告警重置功能
- ✅ 多服务独立计数

## 注意事项

### 1. 配置建议
- 建议设置 MAX_ALERT_COUNT 为 3-10 次
- 根据服务重要性调整告警次数
- 考虑服务恢复时间设置合理的限制

### 2. 监控建议
- 定期检查告警日志
- 关注被静默的服务状态
- 及时处理持续故障的服务

### 3. 兼容性
- 完全兼容原有配置格式
- 不影响现有监控逻辑
- 可选功能，不配置则使用默认值

## 故障排除

### 问题1：告警没有被静默
- 检查 MAX_ALERT_COUNT 配置是否正确
- 确认服务名称匹配
- 查看日志中的告警计数记录

### 问题2：告警计数没有重置
- 确认服务确实已经恢复运行
- 检查进程关键字是否正确
- 查看日志中的重置记录

### 问题3：多服务计数混乱
- 确认服务名称唯一
- 检查配置文件中的服务定义
- 查看日志中各服务的独立计数

## 更新历史

- **v1.0** (2025-07-02): 初始版本，实现基本告警限制功能
  - 支持告警计数和静默
  - 支持自动重置
  - 支持多服务独立计数
  - 完整的测试和演示脚本
