# 路径一致性检查报告

## 🔍 检查结果

### ✅ 已统一的路径

#### 1. 部署目录
- **统一路径**：`/opt/monitor/`
- **状态**：✅ 所有文件一致

#### 2. 日志文件
- **统一路径**：`/opt/monitor/logs/service-monitor.log`
- **状态**：✅ 已修复，代码优先使用此路径

#### 3. 配置文件（部署后）
- **统一路径**：`/opt/monitor/monitor.conf`
- **状态**：✅ 所有文件一致

#### 4. systemd服务文件
- **统一路径**：`/etc/systemd/system/monitor-services.service`
- **状态**：✅ 所有文件一致

### ⚠️ 需要注意的配置文件名

#### 源配置文件
- **当前名称**：`service-monitor-python.conf`
- **部署后名称**：`/opt/monitor/monitor.conf`
- **状态**：✅ 正常，部署时会重命名

## 📋 完整路径映射表

### 部署前（源文件）
```
项目目录/
├── monitor-services.py              # 主脚本
├── service-monitor-python.conf      # 配置文件（源）
├── deploy-monitor.sh                # 部署脚本
├── config-wizard.sh                 # 配置向导
├── monitor-services.service         # systemd模板
└── requirements.txt                 # 依赖文件
```

### 部署后（目标位置）
```
/opt/monitor/                        # 主目录
├── monitor-services.py              # 主脚本
├── monitor.conf                     # 配置文件（重命名）
├── requirements.txt                 # 依赖文件
├── logs/                            # 日志目录
│   └── service-monitor.log          # 监控日志
├── backup/                          # 备份目录
│   └── config-backup/               # 配置备份
└── scripts/                         # 脚本目录
    └── maintenance.sh               # 维护脚本

/etc/systemd/system/
└── monitor-services.service         # systemd服务

/etc/logrotate.d/
└── monitor-services                 # 日志轮转配置
```

## 🔧 路径使用规范

### 1. 脚本中的路径引用
```python
# 日志文件（智能选择）
优先级1: /opt/monitor/logs/service-monitor.log
优先级2: /var/log/service-monitor.log  
优先级3: ./service-monitor.log

# 配置文件
默认: ./service-monitor.conf
部署: /opt/monitor/monitor.conf
```

### 2. 部署脚本中的路径
```bash
# 源文件
service-monitor-python.conf

# 目标文件
/opt/monitor/monitor.conf

# 日志目录
/opt/monitor/logs/
```

### 3. 文档中的路径引用
```bash
# 配置文件
/opt/monitor/monitor.conf

# 日志文件
/opt/monitor/logs/service-monitor.log

# 主脚本
/opt/monitor/monitor-services.py
```

## ✅ 验证命令

### 1. 检查部署后的文件结构
```bash
# 检查主目录
ls -la /opt/monitor/

# 检查日志目录
ls -la /opt/monitor/logs/

# 检查配置文件
cat /opt/monitor/monitor.conf

# 检查systemd服务
systemctl cat monitor-services
```

### 2. 检查路径使用
```bash
# 检查实际使用的日志文件
sudo lsof -p $(pgrep -f monitor-services.py) | grep log

# 检查配置文件加载
sudo journalctl -u monitor-services | grep "加载配置"
```

### 3. 检查权限设置
```bash
# 检查目录权限
ls -ld /opt/monitor/
ls -ld /opt/monitor/logs/

# 检查文件权限
ls -la /opt/monitor/monitor-services.py
ls -la /opt/monitor/monitor.conf
```

## 🎯 路径一致性总结

### ✅ 完全一致的路径
1. **部署目录**：`/opt/monitor/` - 所有文件统一
2. **日志目录**：`/opt/monitor/logs/` - 所有文件统一  
3. **日志文件**：`service-monitor.log` - 所有文件统一
4. **systemd服务**：`monitor-services.service` - 所有文件统一

### ✅ 正常的路径变化
1. **配置文件**：
   - 源文件：`service-monitor-python.conf`
   - 部署后：`/opt/monitor/monitor.conf`
   - 说明：这是正常的重命名，便于管理

### ✅ 智能路径选择
1. **日志文件位置**：
   - 优先：`/opt/monitor/logs/service-monitor.log`
   - 降级：`/var/log/service-monitor.log`
   - 兜底：`./service-monitor.log`

## 📝 使用建议

### 1. 标准部署
```bash
# 使用一键部署脚本（推荐）
sudo ./deploy-monitor.sh
# 所有路径自动配置正确
```

### 2. 手动部署
```bash
# 确保使用正确的文件名
cp service-monitor-python.conf /opt/monitor/monitor.conf
# 注意配置文件重命名
```

### 3. 路径引用
```bash
# 在文档和脚本中统一使用部署后的路径
配置文件: /opt/monitor/monitor.conf
日志文件: /opt/monitor/logs/service-monitor.log
主脚本: /opt/monitor/monitor-services.py
```

## 🎉 结论

经过检查和修复，所有路径已实现完全一致：

1. **✅ 代码实现**：智能路径选择，优先使用标准路径
2. **✅ 部署脚本**：自动配置正确的路径和权限
3. **✅ 文档说明**：所有路径引用统一
4. **✅ 配置模板**：路径配置标准化

用户现在可以放心使用，所有路径都是一致和正确的！
