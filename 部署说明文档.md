# Python服务监控系统部署说明

## 📋 概述

本文档详细说明如何部署 `monitor-services.py` Python服务监控系统，包括环境准备、配置文件设置、systemctl服务配置等完整部署流程。

## 🔧 环境要求

### 系统要求
- **操作系统**：Linux (推荐 CentOS 7+, Ubuntu 18.04+)
- **Python版本**：Python 3.6+
- **权限要求**：root权限（用于创建系统服务）

### 依赖包
```bash
# Python依赖
pip install requests>=2.25.0
pip install psutil>=5.8.0

# 或使用requirements.txt
pip install -r requirements.txt
```

## 📁 部署目录结构

```
/opt/monitor/                    # 推荐部署目录
├── monitor-services.py          # 主监控脚本
├── requirements.txt             # Python依赖
├── monitor.conf                 # 配置文件
├── logs/                        # 日志目录
│   └── service-monitor.log      # 监控日志
└── backup/                      # 备份目录
    └── config-backup/           # 配置备份
```

## 🚀 快速部署

### 1. 创建部署用户和目录

```bash
# 创建专用用户（推荐）
sudo useradd -r -s /bin/false -d /opt/monitor monitor

# 创建部署目录
sudo mkdir -p /opt/monitor/{logs,backup/config-backup}
sudo chown -R monitor:monitor /opt/monitor
sudo chmod 755 /opt/monitor
```

### 2. 部署文件

```bash
# 复制主要文件到部署目录
sudo cp monitor-services.py /opt/monitor/
sudo cp requirements.txt /opt/monitor/
sudo cp service-monitor-python.conf /opt/monitor/monitor.conf

# 设置文件权限
sudo chown monitor:monitor /opt/monitor/*
sudo chmod 755 /opt/monitor/monitor-services.py
sudo chmod 644 /opt/monitor/monitor.conf
sudo chmod 644 /opt/monitor/requirements.txt
```

### 3. 安装Python依赖

```bash
# 切换到monitor用户安装依赖
sudo -u monitor bash -c "cd /opt/monitor && pip3 install --user -r requirements.txt"

# 或者系统级安装
sudo pip3 install -r /opt/monitor/requirements.txt
```

## ⚙️ 配置文件设置

### 编辑配置文件

```bash
sudo vim /opt/monitor/monitor.conf
```

### 基础配置示例

```bash
# ================================
# 基础监控配置
# ================================
CHECK_INTERVAL=60                # 检查间隔（秒）
MAX_ALERT_COUNT=5               # 最大告警次数
DING_API_URL=https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN

# 监控服务列表
SERVICES=GATEWAY AUTH SYSTEM

# ================================
# 网关服务配置
# ================================
GATEWAY_NAME=API网关服务
GATEWAY_JAR_PATH=/app/gateway/gateway-service.jar
GATEWAY_JAVA_OPTS=-Xms1g -Xmx2g -XX:+UseG1GC
GATEWAY_PROCESS_KEYWORD=gateway-service.jar

# ================================
# 认证服务配置
# ================================
AUTH_NAME=用户认证服务
AUTH_JAR_PATH=/app/auth/auth-service.jar
AUTH_JAVA_OPTS=-Xms512m -Xmx1g
AUTH_PROCESS_KEYWORD=auth-service.jar

# ================================
# 系统服务配置
# ================================
SYSTEM_NAME=系统管理服务
SYSTEM_JAR_PATH=/app/system/system-service.jar
SYSTEM_JAVA_OPTS=-Xms256m -Xmx512m
SYSTEM_PROCESS_KEYWORD=system-service.jar
```

### 配置验证

```bash
# 测试配置文件
sudo -u monitor python3 /opt/monitor/monitor-services.py /opt/monitor/monitor.conf --help

# 检查配置语法
sudo -u monitor python3 -c "
import sys
sys.path.append('/opt/monitor')
from monitor_services import ServiceMonitor
monitor = ServiceMonitor('/opt/monitor/monitor.conf')
print('配置文件验证成功')
"
```

## 🔧 Systemd服务配置

### 1. 创建systemd服务文件

```bash
sudo vim /etc/systemd/system/monitor-services.service
```

### 2. 服务文件内容

```ini
[Unit]
Description=Java Services Monitor
Documentation=https://github.com/your-repo/monitor-services
After=network.target network-online.target
Wants=network-online.target

[Service]
Type=simple
User=monitor
Group=monitor
WorkingDirectory=/opt/monitor
ExecStart=/usr/bin/python3 /opt/monitor/monitor-services.py /opt/monitor/monitor.conf
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# 环境变量
Environment=PYTHONPATH=/opt/monitor
Environment=PYTHONUNBUFFERED=1

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=monitor-services

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/monitor

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
```

### 3. 启用和启动服务

```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启用服务（开机自启）
sudo systemctl enable monitor-services

# 启动服务
sudo systemctl start monitor-services

# 检查服务状态
sudo systemctl status monitor-services
```

## 📊 服务管理命令

### 基本操作

```bash
# 启动服务
sudo systemctl start monitor-services

# 停止服务
sudo systemctl stop monitor-services

# 重启服务
sudo systemctl restart monitor-services

# 重新加载配置
sudo systemctl reload monitor-services

# 查看服务状态
sudo systemctl status monitor-services

# 查看服务是否开机自启
sudo systemctl is-enabled monitor-services
```

### 日志查看

```bash
# 查看实时日志
sudo journalctl -u monitor-services -f

# 查看最近日志
sudo journalctl -u monitor-services -n 50

# 查看今天的日志
sudo journalctl -u monitor-services --since today

# 查看指定时间段日志
sudo journalctl -u monitor-services --since "2025-07-01 00:00:00" --until "2025-07-02 00:00:00"

# 查看应用日志文件
sudo tail -f /opt/monitor/logs/service-monitor.log
```

## 🔍 部署验证

### 1. 功能测试

```bash
# 手动运行测试
sudo -u monitor python3 /opt/monitor/monitor-services.py /opt/monitor/monitor.conf

# 检查进程
ps aux | grep monitor-services

# 检查端口（如果有）
netstat -tlnp | grep python
```

### 2. 服务测试

```bash
# 测试服务启动
sudo systemctl start monitor-services
sleep 5
sudo systemctl status monitor-services

# 测试服务停止
sudo systemctl stop monitor-services
sudo systemctl status monitor-services

# 测试开机自启
sudo systemctl is-enabled monitor-services
```

### 3. 监控测试

```bash
# 查看监控日志
sudo tail -f /opt/monitor/logs/service-monitor.log

# 模拟服务故障（停止一个被监控的服务）
# 观察监控系统是否能检测到并发送告警

# 检查钉钉通知是否正常
```

## 🛡️ 安全配置

### 1. 文件权限

```bash
# 设置正确的文件权限
sudo chmod 755 /opt/monitor/monitor-services.py
sudo chmod 644 /opt/monitor/monitor.conf
sudo chmod 644 /etc/systemd/system/monitor-services.service
sudo chmod 755 /opt/monitor/logs
sudo chmod 644 /opt/monitor/logs/service-monitor.log
```

### 2. 用户权限

```bash
# monitor用户权限设置
sudo usermod -a -G systemd-journal monitor  # 允许读取systemd日志

# 如果需要重启其他服务，添加sudo权限
sudo visudo
# 添加：monitor ALL=(ALL) NOPASSWD: /bin/systemctl restart your-service
```

### 3. 防火墙配置

```bash
# 如果监控系统需要网络访问
sudo firewall-cmd --permanent --add-port=8080/tcp  # 示例端口
sudo firewall-cmd --reload
```

## 📝 日志轮转配置

### 创建logrotate配置

```bash
sudo vim /etc/logrotate.d/monitor-services
```

### logrotate配置内容

```
/opt/monitor/logs/service-monitor.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 monitor monitor
    postrotate
        /bin/systemctl reload monitor-services > /dev/null 2>&1 || true
    endscript
}
```

## 🔄 备份和恢复

### 1. 配置备份脚本

```bash
sudo vim /opt/monitor/backup-config.sh
```

```bash
#!/bin/bash
# 配置文件备份脚本

BACKUP_DIR="/opt/monitor/backup/config-backup"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份
cp /opt/monitor/monitor.conf "$BACKUP_DIR/monitor.conf.$DATE"
cp /etc/systemd/system/monitor-services.service "$BACKUP_DIR/monitor-services.service.$DATE"

# 保留最近30天的备份
find "$BACKUP_DIR" -name "*.conf.*" -mtime +30 -delete
find "$BACKUP_DIR" -name "*.service.*" -mtime +30 -delete

echo "配置备份完成: $DATE"
```

### 2. 定时备份

```bash
# 添加到crontab
sudo crontab -e

# 每天凌晨2点备份
0 2 * * * /opt/monitor/backup-config.sh
```

## 🚨 故障排除

### 常见问题

1. **服务启动失败**
```bash
# 检查配置文件语法
sudo -u monitor python3 /opt/monitor/monitor-services.py /opt/monitor/monitor.conf --help

# 检查权限
ls -la /opt/monitor/
sudo systemctl status monitor-services
```

2. **Python依赖问题**
```bash
# 检查依赖
sudo -u monitor python3 -c "import requests, psutil; print('依赖正常')"

# 重新安装依赖
sudo -u monitor pip3 install --user -r /opt/monitor/requirements.txt
```

3. **权限问题**
```bash
# 检查用户权限
sudo -u monitor ls -la /opt/monitor/
sudo -u monitor touch /opt/monitor/test.txt
```

### 调试模式

```bash
# 前台运行调试
sudo -u monitor python3 /opt/monitor/monitor-services.py /opt/monitor/monitor.conf

# 增加详细日志
sudo systemctl edit monitor-services
# 添加：
# [Service]
# Environment=PYTHONUNBUFFERED=1
# Environment=DEBUG=1
```

## ✅ 部署检查清单

- [ ] 创建monitor用户和目录
- [ ] 复制脚本和配置文件
- [ ] 安装Python依赖
- [ ] 配置监控服务列表
- [ ] 配置钉钉通知
- [ ] 创建systemd服务文件
- [ ] 启用和启动服务
- [ ] 验证服务状态
- [ ] 测试监控功能
- [ ] 配置日志轮转
- [ ] 设置配置备份
- [ ] 检查安全权限

## 📞 技术支持

如果在部署过程中遇到问题，可以：

1. 查看详细日志：`sudo journalctl -u monitor-services -f`
2. 检查配置文件：`python3 monitor-services.py config.conf --help`
3. 运行测试脚本：`python3 test_monitor.py`

## 🎯 生产环境优化

### 1. 性能调优

```bash
# 系统参数优化
echo 'vm.swappiness=10' >> /etc/sysctl.conf
echo 'fs.file-max=65536' >> /etc/sysctl.conf
sysctl -p

# 进程限制优化
echo 'monitor soft nofile 65536' >> /etc/security/limits.conf
echo 'monitor hard nofile 65536' >> /etc/security/limits.conf
```

### 2. 监控告警优化

```bash
# 配置文件优化建议
CHECK_INTERVAL=30          # 生产环境建议30-60秒
MAX_ALERT_COUNT=3         # 生产环境建议3-5次
```

### 3. 资源监控

```bash
# 监控脚本资源使用
top -p $(pgrep -f monitor-services.py)
ps aux | grep monitor-services

# 监控日志文件大小
du -sh /opt/monitor/logs/
```

## 🔧 高级配置

### 1. 多环境部署

```bash
# 开发环境
/opt/monitor-dev/
├── monitor-services.py
├── monitor-dev.conf
└── logs/

# 测试环境
/opt/monitor-test/
├── monitor-services.py
├── monitor-test.conf
└── logs/

# 生产环境
/opt/monitor-prod/
├── monitor-services.py
├── monitor-prod.conf
└── logs/
```

### 2. 集群部署

```bash
# 主节点配置
SERVICES=GATEWAY AUTH SYSTEM ORDER

# 备节点配置
SERVICES=PAYMENT NOTIFICATION LOG
```

### 3. 配置模板

```bash
# 创建配置模板
sudo vim /opt/monitor/templates/service-template.conf

# 模板内容
{SERVICE_NAME}_NAME={SERVICE_DISPLAY_NAME}
{SERVICE_NAME}_JAR_PATH={JAR_PATH}
{SERVICE_NAME}_JAVA_OPTS={JAVA_OPTS}
{SERVICE_NAME}_PROCESS_KEYWORD={PROCESS_KEYWORD}
```

## 📈 监控指标

### 1. 系统指标监控

```bash
# CPU使用率
top -p $(pgrep -f monitor-services.py) -n 1 | grep monitor

# 内存使用
ps -o pid,ppid,cmd,%mem,%cpu -p $(pgrep -f monitor-services.py)

# 磁盘使用
df -h /opt/monitor/
```

### 2. 应用指标监控

```bash
# 监控循环次数
grep "监控循环" /opt/monitor/logs/service-monitor.log | tail -5

# 告警统计
grep "告警" /opt/monitor/logs/service-monitor.log | wc -l

# 重启统计
grep "重启" /opt/monitor/logs/service-monitor.log | wc -l
```

## 🔄 升级和维护

### 1. 版本升级

```bash
# 备份当前版本
sudo cp /opt/monitor/monitor-services.py /opt/monitor/backup/monitor-services.py.$(date +%Y%m%d)

# 停止服务
sudo systemctl stop monitor-services

# 更新脚本
sudo cp new-monitor-services.py /opt/monitor/monitor-services.py
sudo chown monitor:monitor /opt/monitor/monitor-services.py

# 启动服务
sudo systemctl start monitor-services

# 验证升级
sudo systemctl status monitor-services
```

### 2. 配置更新

```bash
# 备份配置
sudo cp /opt/monitor/monitor.conf /opt/monitor/backup/monitor.conf.$(date +%Y%m%d)

# 更新配置
sudo vim /opt/monitor/monitor.conf

# 重新加载配置
sudo systemctl reload monitor-services
```

### 3. 定期维护

```bash
# 创建维护脚本
sudo vim /opt/monitor/maintenance.sh
```

```bash
#!/bin/bash
# 监控系统维护脚本

echo "开始监控系统维护..."

# 检查服务状态
systemctl is-active monitor-services

# 检查日志文件大小
LOG_SIZE=$(du -m /opt/monitor/logs/service-monitor.log | cut -f1)
if [ $LOG_SIZE -gt 100 ]; then
    echo "警告: 日志文件过大 (${LOG_SIZE}MB)"
fi

# 检查磁盘空间
DISK_USAGE=$(df /opt/monitor | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "警告: 磁盘使用率过高 (${DISK_USAGE}%)"
fi

# 清理临时文件
find /opt/monitor/backup -name "*.tmp" -mtime +7 -delete

echo "维护完成"
```

## 🚀 自动化部署脚本

### 创建一键部署脚本

```bash
sudo vim /tmp/deploy-monitor.sh
```

```bash
#!/bin/bash
# 监控系统一键部署脚本

set -e

echo "=== Python服务监控系统部署脚本 ==="

# 检查root权限
if [ "$EUID" -ne 0 ]; then
    echo "请使用root权限运行此脚本"
    exit 1
fi

# 创建用户和目录
echo "1. 创建monitor用户和目录..."
useradd -r -s /bin/false -d /opt/monitor monitor 2>/dev/null || true
mkdir -p /opt/monitor/{logs,backup/config-backup}
chown -R monitor:monitor /opt/monitor

# 安装依赖
echo "2. 安装Python依赖..."
pip3 install requests psutil

# 复制文件
echo "3. 复制监控文件..."
cp monitor-services.py /opt/monitor/
cp requirements.txt /opt/monitor/
cp service-monitor-python.conf /opt/monitor/monitor.conf

# 设置权限
chown monitor:monitor /opt/monitor/*
chmod 755 /opt/monitor/monitor-services.py
chmod 644 /opt/monitor/monitor.conf

# 创建systemd服务
echo "4. 创建systemd服务..."
cat > /etc/systemd/system/monitor-services.service << 'EOF'
[Unit]
Description=Java Services Monitor
After=network.target

[Service]
Type=simple
User=monitor
Group=monitor
WorkingDirectory=/opt/monitor
ExecStart=/usr/bin/python3 /opt/monitor/monitor-services.py /opt/monitor/monitor.conf
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

# 启用服务
echo "5. 启用和启动服务..."
systemctl daemon-reload
systemctl enable monitor-services
systemctl start monitor-services

# 验证部署
echo "6. 验证部署..."
sleep 3
if systemctl is-active --quiet monitor-services; then
    echo "✅ 部署成功！监控服务已启动"
    echo "查看状态: systemctl status monitor-services"
    echo "查看日志: journalctl -u monitor-services -f"
else
    echo "❌ 部署失败，请检查日志"
    systemctl status monitor-services
fi
```

### 使用部署脚本

```bash
# 赋予执行权限
chmod +x /tmp/deploy-monitor.sh

# 执行部署
sudo /tmp/deploy-monitor.sh
```

## 📋 部署后验证清单

### 1. 服务验证

```bash
# 检查服务状态
sudo systemctl status monitor-services
sudo systemctl is-enabled monitor-services

# 检查进程
ps aux | grep monitor-services
pgrep -f monitor-services.py
```

### 2. 功能验证

```bash
# 检查配置加载
sudo journalctl -u monitor-services -n 20 | grep "加载配置"

# 检查监控循环
sudo journalctl -u monitor-services -n 20 | grep "监控循环"

# 检查日志文件
ls -la /opt/monitor/logs/
tail -10 /opt/monitor/logs/service-monitor.log
```

### 3. 网络验证

```bash
# 测试钉钉通知（如果配置了）
curl -X POST "YOUR_DING_API_URL" \
  -H "Content-Type: application/json" \
  -d '{"msgtype":"text","text":{"content":"监控系统部署测试"}}'
```

## 🎉 部署完成

恭喜！Python服务监控系统已成功部署。

### 下一步操作

1. **配置监控服务**：编辑 `/opt/monitor/monitor.conf` 添加要监控的服务
2. **配置钉钉通知**：设置 `DING_API_URL` 参数
3. **测试监控功能**：停止一个被监控的服务，观察是否能自动重启
4. **设置日志轮转**：配置 logrotate 防止日志文件过大
5. **定期维护**：设置定时任务进行系统维护

### 常用命令

```bash
# 查看服务状态
sudo systemctl status monitor-services

# 查看实时日志
sudo journalctl -u monitor-services -f

# 重启服务
sudo systemctl restart monitor-services

# 编辑配置
sudo vim /opt/monitor/monitor.conf
sudo systemctl reload monitor-services
```

---

*部署完成后，监控系统将自动运行，定期检查配置的Java服务状态，并在服务异常时自动重启和发送告警通知。系统具有7天自动日志清理功能，无需人工维护。*
