#!/usr/bin/env python3
"""
快速测试告警限制功能
"""

import sys
import time
import importlib.util

# 动态导入monitor-services.py
spec = importlib.util.spec_from_file_location("monitor_services", "monitor-services.py")
monitor_services = importlib.util.module_from_spec(spec)
spec.loader.exec_module(monitor_services)

def quick_test():
    """快速测试告警限制功能"""
    print("🚀 快速测试告警限制功能")
    print("=" * 50)
    
    # 创建测试配置
    config_content = """CHECK_INTERVAL=5
MAX_ALERT_COUNT=3
DING_API_URL=
SERVICES=QUICK_TEST

QUICK_TEST_NAME=快速测试服务
QUICK_TEST_JAR_PATH=/tmp/quick-test.jar
QUICK_TEST_JAVA_OPTS=
QUICK_TEST_PROCESS_KEYWORD=quick-test-nonexistent
"""
    
    with open('quick-test.conf', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    # 初始化监控器
    monitor = monitor_services.ServiceMonitor('quick-test.conf')
    service_name = "快速测试服务"
    
    print(f"✓ 监控器初始化完成，最大告警次数: {monitor.max_alert_count}")
    
    # 测试1: 连续告警直到静默
    print("\n📋 测试1: 连续告警直到静默")
    print("-" * 30)
    
    for i in range(5):
        count_before = monitor.alert_counters.get(service_name, 0)
        silenced_before = monitor.is_alert_silenced(service_name)
        
        if not silenced_before:
            result = monitor.send_alert_with_limit(service_name, f"测试告警{i+1}")
            print(f"第{i+1}次: 发送{'成功' if result else '失败'} (计数: {count_before} → {monitor.alert_counters.get(service_name, 0)})")
        else:
            print(f"第{i+1}次: 已静默，跳过发送")
    
    # 测试2: 重置功能
    print("\n📋 测试2: 重置告警计数")
    print("-" * 30)
    
    print(f"重置前: 计数={monitor.alert_counters.get(service_name, 0)}, 静默={monitor.is_alert_silenced(service_name)}")
    monitor.reset_alert_counter(service_name)
    print(f"重置后: 计数={monitor.alert_counters.get(service_name, 0)}, 静默={monitor.is_alert_silenced(service_name)}")
    
    # 测试3: 重置后新告警
    print("\n📋 测试3: 重置后新告警")
    print("-" * 30)
    
    result = monitor.send_alert_with_limit(service_name, "重置后的新告警")
    print(f"新告警: 发送{'成功' if result else '失败'} (计数: {monitor.alert_counters.get(service_name, 0)})")
    
    print("\n✅ 快速测试完成！")
    print("\n功能验证:")
    print("• ✓ 告警计数正常")
    print("• ✓ 告警限制正常")
    print("• ✓ 告警静默正常")
    print("• ✓ 计数重置正常")

if __name__ == "__main__":
    try:
        quick_test()
    except Exception as e:
        print(f"测试失败: {e}")
        sys.exit(1)
