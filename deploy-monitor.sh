#!/bin/bash
# Python服务监控系统一键部署脚本
# 版本: 2.0
# 作者: Monitor Team
# 日期: 2025-07-02

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查函数
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root权限运行此脚本"
        echo "使用方法: sudo $0"
        exit 1
    fi
}

check_python() {
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装，请先安装Python3"
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
    log_info "检测到Python版本: $PYTHON_VERSION"
}

check_pip() {
    if ! command -v pip3 &> /dev/null; then
        log_warning "pip3 未安装，正在安装..."
        if command -v yum &> /dev/null; then
            yum install -y python3-pip
        elif command -v apt &> /dev/null; then
            apt update && apt install -y python3-pip
        else
            log_error "无法自动安装pip3，请手动安装"
            exit 1
        fi
    fi
}

check_files() {
    local missing_files=()
    
    if [ ! -f "monitor-services.py" ]; then
        missing_files+=("monitor-services.py")
    fi
    
    if [ ! -f "requirements.txt" ]; then
        missing_files+=("requirements.txt")
    fi
    
    if [ ! -f "service-monitor-python.conf" ]; then
        missing_files+=("service-monitor-python.conf")
    fi
    
    if [ ${#missing_files[@]} -ne 0 ]; then
        log_error "缺少必要文件: ${missing_files[*]}"
        log_error "请确保在包含监控脚本的目录中运行此部署脚本"
        exit 1
    fi
}

# 部署函数
create_user_and_dirs() {
    log_info "创建monitor用户和目录..."

    # 创建用户
    if ! id "monitor" &>/dev/null; then
        useradd -r -s /bin/false -d /opt/monitor monitor
        log_success "创建monitor用户成功"
    else
        log_info "monitor用户已存在"
    fi

    # 使用root权限创建目录结构
    log_info "创建目录结构..."
    mkdir -p /opt/monitor/{logs,backup/config-backup,scripts}

    # 设置目录权限
    chmod 755 /opt/monitor
    chmod 755 /opt/monitor/logs
    chmod 755 /opt/monitor/backup
    chmod 755 /opt/monitor/scripts

    # 设置所有者
    chown -R monitor:monitor /opt/monitor

    log_success "目录创建完成"
    log_info "目录结构: /opt/monitor/{logs,backup/config-backup,scripts}"
}

install_dependencies() {
    log_info "安装Python依赖..."
    
    # 安装系统级依赖
    pip3 install requests psutil
    
    # 为monitor用户安装依赖
    sudo -u monitor pip3 install --user requests psutil
    
    log_success "依赖安装完成"
}

deploy_files() {
    log_info "部署监控文件..."
    
    # 复制主要文件
    cp monitor-services.py /opt/monitor/
    cp requirements.txt /opt/monitor/
    cp service-monitor-python.conf /opt/monitor/monitor.conf
    
    # 复制测试文件（如果存在）
    if [ -f "test_monitor.py" ]; then
        cp test_monitor.py /opt/monitor/scripts/
    fi
    
    if [ -f "test_alert_limit.py" ]; then
        cp test_alert_limit.py /opt/monitor/scripts/
    fi
    
    # 设置权限
    chown monitor:monitor /opt/monitor/*
    chown -R monitor:monitor /opt/monitor/scripts/
    chmod 755 /opt/monitor/monitor-services.py
    chmod 644 /opt/monitor/monitor.conf
    chmod 644 /opt/monitor/requirements.txt
    
    log_success "文件部署完成"
}

create_systemd_service() {
    log_info "创建systemd服务..."
    
    cat > /etc/systemd/system/monitor-services.service << 'EOF'
[Unit]
Description=Java Services Monitor
Documentation=https://github.com/your-repo/monitor-services
After=network.target network-online.target
Wants=network-online.target

[Service]
Type=simple
User=monitor
Group=monitor
WorkingDirectory=/opt/monitor
ExecStart=/usr/bin/python3 /opt/monitor/monitor-services.py /opt/monitor/monitor.conf
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# 环境变量
Environment=PYTHONPATH=/opt/monitor
Environment=PYTHONUNBUFFERED=1

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=monitor-services

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/monitor

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF

    log_success "systemd服务文件创建完成"
}

setup_logrotate() {
    log_info "配置日志轮转..."
    
    cat > /etc/logrotate.d/monitor-services << 'EOF'
/opt/monitor/logs/service-monitor.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 monitor monitor
    copytruncate
    postrotate
        /bin/systemctl reload monitor-services > /dev/null 2>&1 || true
    endscript
}
EOF

    log_success "日志轮转配置完成"
}

create_maintenance_script() {
    log_info "创建维护脚本..."
    
    cat > /opt/monitor/scripts/maintenance.sh << 'EOF'
#!/bin/bash
# 监控系统维护脚本

echo "开始监控系统维护..."

# 检查服务状态
if systemctl is-active --quiet monitor-services; then
    echo "✓ 监控服务运行正常"
else
    echo "✗ 监控服务异常"
fi

# 检查日志文件大小
LOG_FILE="/opt/monitor/logs/service-monitor.log"
if [ -f "$LOG_FILE" ]; then
    LOG_SIZE=$(du -m "$LOG_FILE" | cut -f1)
    if [ $LOG_SIZE -gt 100 ]; then
        echo "警告: 日志文件过大 (${LOG_SIZE}MB)"
    else
        echo "✓ 日志文件大小正常 (${LOG_SIZE}MB)"
    fi
else
    echo "✗ 日志文件不存在: $LOG_FILE"
fi

# 检查磁盘空间
DISK_USAGE=$(df /opt/monitor | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "警告: 磁盘使用率过高 (${DISK_USAGE}%)"
else
    echo "✓ 磁盘使用率正常 (${DISK_USAGE}%)"
fi

# 清理临时文件
find /opt/monitor/backup -name "*.tmp" -mtime +7 -delete 2>/dev/null || true

echo "维护完成"
EOF

    chmod +x /opt/monitor/scripts/maintenance.sh
    chown monitor:monitor /opt/monitor/scripts/maintenance.sh
    
    log_success "维护脚本创建完成"
}

start_service() {
    log_info "启用和启动服务..."
    
    # 重新加载systemd
    systemctl daemon-reload
    
    # 启用服务
    systemctl enable monitor-services
    
    # 启动服务
    systemctl start monitor-services
    
    log_success "服务启动完成"
}

verify_deployment() {
    log_info "验证部署..."
    
    # 等待服务启动
    sleep 3
    
    # 检查服务状态
    if systemctl is-active --quiet monitor-services; then
        log_success "✓ 监控服务运行正常"
    else
        log_error "✗ 监控服务启动失败"
        systemctl status monitor-services
        return 1
    fi
    
    # 检查服务是否开机自启
    if systemctl is-enabled --quiet monitor-services; then
        log_success "✓ 服务已设置为开机自启"
    else
        log_warning "✗ 服务未设置为开机自启"
    fi
    
    # 检查日志
    LOG_FILE="/opt/monitor/logs/service-monitor.log"
    if [ -f "$LOG_FILE" ]; then
        log_success "✓ 日志文件创建成功: $LOG_FILE"
    else
        log_warning "✗ 日志文件未创建，可能需要等待: $LOG_FILE"
    fi
    
    # 检查进程
    if pgrep -f monitor-services.py > /dev/null; then
        log_success "✓ 监控进程运行正常"
    else
        log_warning "✗ 监控进程未找到"
    fi
}

show_next_steps() {
    echo ""
    echo "=========================================="
    log_success "部署完成！"
    echo "=========================================="
    echo ""
    echo "下一步操作："
    echo "1. 编辑配置文件: vim /opt/monitor/monitor.conf"
    echo "2. 配置要监控的服务和钉钉通知"
    echo "3. 重新加载配置: systemctl reload monitor-services"
    echo ""
    echo "常用命令："
    echo "• 查看服务状态: systemctl status monitor-services"
    echo "• 查看实时日志: journalctl -u monitor-services -f"
    echo "• 查看应用日志: tail -f /opt/monitor/logs/service-monitor.log"
    echo "• 重启服务: systemctl restart monitor-services"
    echo "• 运行维护: /opt/monitor/scripts/maintenance.sh"
    echo ""
    echo "配置文件位置: /opt/monitor/monitor.conf"
    echo "日志文件位置: /opt/monitor/logs/service-monitor.log"
    echo ""
}

# 主函数
main() {
    echo "=========================================="
    echo "Python服务监控系统一键部署脚本"
    echo "版本: 2.0"
    echo "=========================================="
    echo ""
    
    # 检查环境
    log_info "检查部署环境..."
    check_root
    check_python
    check_pip
    check_files
    log_success "环境检查通过"
    echo ""
    
    # 执行部署
    create_user_and_dirs
    install_dependencies
    deploy_files
    create_systemd_service
    setup_logrotate
    create_maintenance_script
    start_service
    
    echo ""
    
    # 验证部署
    if verify_deployment; then
        show_next_steps
    else
        log_error "部署验证失败，请检查错误信息"
        exit 1
    fi
}

# 执行主函数
main "$@"
