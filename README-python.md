# Python版本Java服务监控脚本

这是原bash版本服务监控脚本的Python重写版本，提供了相同的功能但具有更好的可维护性和扩展性。

## 🚀 功能特性

- **服务监控**: 自动检测Java服务进程状态
- **自动重启**: 服务停止时自动重启
- **钉钉通知**: 服务状态变化时发送钉钉消息
- **日志管理**: 自动记录监控日志并定期清理
- **配置兼容**: 兼容原bash版本的配置文件格式
- **优雅退出**: 支持信号处理，优雅停止监控

## 📋 系统要求

- Python 3.6+
- Linux/Unix系统
- 具有执行Java进程的权限

## 🔧 安装和配置

### 1. 安装Python依赖

```bash
# 安装依赖包
pip install -r requirements.txt

# 或者手动安装
pip install requests psutil
```

### 2. 配置服务监控

编辑配置文件 `service-monitor-python.conf`：

```bash
# 检查间隔（秒）
CHECK_INTERVAL=60

# 钉钉通知配置
DING_API_URL=https://oapi.dingtalk.com/robot/send?access_token=your_token

# 要监控的服务列表
SERVICES=GATEWAY AUTH SYSTEM

# 服务配置示例
GATEWAY_NAME=nnb-gateway 服务
GATEWAY_JAR_PATH=/path/to/nnb-gateway.jar
GATEWAY_JAVA_OPTS=-Xms512m -Xmx1024m
GATEWAY_PROCESS_KEYWORD=nnb-gateway.jar
```

### 3. 运行监控脚本

```bash
# 使用默认配置文件
python monitor-services.py

# 指定配置文件
python monitor-services.py /path/to/your/config.conf

# 后台运行
nohup python monitor-services.py > monitor.log 2>&1 &
```

## 📊 监控功能

### 服务状态检查
- 使用 `psutil` 库检查进程状态
- 根据进程关键字匹配Java服务
- 支持多进程环境下的精确匹配

### 自动重启机制
- 检测到服务停止时立即重启
- 支持自定义Java启动参数
- 15秒后验证重启是否成功

### 钉钉通知
支持以下通知场景：
- 🔔 监控脚本启动
- ⚠️ 服务停止告警
- ✅ 服务重启成功
- ❌ 服务重启失败
- 🧹 日志清理完成

### 日志管理
- 自动记录所有监控活动
- 每7天自动清理旧日志文件
- 支持多个日志文件位置

## 🛠️ 高级配置

### 自定义日志位置

修改脚本中的日志路径：

```python
self.log_file = "/var/log/service-monitor.log"
self.log_cleanup_flag = "/var/log/.log_cleanup_flag"
```

### 调整监控间隔

在配置文件中设置：

```bash
CHECK_INTERVAL=30  # 30秒检查一次
```

### 配置Java启动参数

```bash
SERVICE_JAVA_OPTS=-Xms512m -Xmx1024m -XX:MetaspaceSize=128m
```

## 🔄 与Bash版本的差异

### 优势
1. **更好的错误处理**: Python的异常处理机制更完善
2. **跨平台支持**: 可以在不同操作系统上运行
3. **依赖管理**: 使用pip管理依赖包
4. **代码可读性**: Python代码更易于理解和维护
5. **扩展性**: 更容易添加新功能

### 兼容性
- 配置文件格式完全兼容
- 钉钉通知格式相同
- 日志格式保持一致
- 功能行为完全相同

## 🚦 使用示例

### 基本使用

```bash
# 启动监控
python monitor-services.py service-monitor-python.conf
```

### 作为系统服务运行

创建systemd服务文件 `/etc/systemd/system/python-service-monitor.service`：

```ini
[Unit]
Description=Python Service Monitor
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/monitor
ExecStart=/usr/bin/python3 /opt/monitor/monitor-services.py /opt/monitor/service-monitor-python.conf
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启用服务：

```bash
systemctl daemon-reload
systemctl enable python-service-monitor.service
systemctl start python-service-monitor.service
```

## 🐛 故障排除

### 常见问题

1. **权限问题**
   ```bash
   # 确保脚本有执行权限
   chmod +x monitor-services.py
   
   # 确保有写入日志文件的权限
   sudo chown $USER:$USER /var/log/service-monitor.log
   ```

2. **依赖包问题**
   ```bash
   # 检查依赖是否安装
   pip list | grep -E "(requests|psutil)"
   
   # 重新安装依赖
   pip install --upgrade requests psutil
   ```

3. **配置文件问题**
   ```bash
   # 检查配置文件格式
   python -c "
   config = {}
   with open('service-monitor-python.conf') as f:
       for line in f:
           if '=' in line and not line.startswith('#'):
               key, value = line.strip().split('=', 1)
               config[key] = value
   print('配置加载成功')
   "
   ```

### 调试模式

添加详细日志输出：

```python
# 在脚本开头添加
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📈 性能监控

### 资源使用情况

```bash
# 查看Python进程资源使用
ps aux | grep monitor-services.py

# 查看内存使用
python -c "
import psutil
import os
process = psutil.Process(os.getpid())
print(f'内存使用: {process.memory_info().rss / 1024 / 1024:.2f} MB')
"
```

## 🔐 安全考虑

1. **配置文件权限**: 确保配置文件不被普通用户读取
2. **日志文件权限**: 限制日志文件的访问权限
3. **网络安全**: 钉钉webhook使用HTTPS加密传输
4. **进程权限**: 建议使用专用用户运行监控脚本

## 📞 技术支持

如果遇到问题，请检查：

1. Python版本是否符合要求
2. 依赖包是否正确安装
3. 配置文件格式是否正确
4. 日志文件权限是否正确
5. 网络连接是否正常

## 🔄 迁移指南

从bash版本迁移到Python版本：

1. 安装Python依赖
2. 复制现有配置文件
3. 测试Python版本功能
4. 停止bash版本服务
5. 启动Python版本服务
6. 验证监控功能正常
