# Python服务监控系统 - 使用说明

## 📋 功能概述

Python服务监控系统是一个轻量级的Java服务监控工具，具有以下核心功能：

### 🔍 监控功能
- **自动检测**：定期检查Java服务进程状态
- **自动重启**：服务异常时自动重启
- **告警通知**：支持钉钉机器人通知
- **告警限制**：每服务最多告警5次，防止告警轰炸
- **日志管理**：自动清理7天前的日志，保持系统整洁

### 🛡️ 安全特性
- **专用用户**：使用monitor用户运行，权限隔离
- **开机自启**：systemd服务管理，系统重启后自动启动
- **资源限制**：内置资源使用限制，防止系统负载过高

## 🚀 一键部署

### 1. 快速部署
```bash
# 下载部署脚本并运行
sudo ./deploy-monitor.sh
```

### 2. 配置服务
```bash
# 运行配置向导
sudo ./config-wizard.sh
```

### 3. 验证部署
```bash
# 检查服务状态
sudo systemctl status monitor-services
```

## ⚙️ 配置说明

### 重要文件位置
```
配置文件: /opt/monitor/monitor.conf
日志文件: /opt/monitor/logs/service-monitor.log
```

### 基础配置
```bash
# 监控间隔（秒）
CHECK_INTERVAL=60

# 最大告警次数
MAX_ALERT_COUNT=5

# 钉钉通知URL
DING_API_URL=https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN

# 监控服务列表
SERVICES=GATEWAY AUTH SYSTEM
```

### 服务配置示例
```bash
# 网关服务
GATEWAY_NAME=API网关服务
GATEWAY_JAR_PATH=/app/gateway/gateway.jar
GATEWAY_JAVA_OPTS=-Xms1g -Xmx2g
GATEWAY_PROCESS_KEYWORD=gateway.jar
```

## 🔧 服务管理

### 启动和停止
```bash
# 启动服务
sudo systemctl start monitor-services

# 停止服务
sudo systemctl stop monitor-services

# 重启服务
sudo systemctl restart monitor-services

# 查看状态
sudo systemctl status monitor-services
```

### 配置重载
```bash
# 修改配置后重新加载
sudo systemctl reload monitor-services
```

### 开机自启
```bash
# 启用开机自启（部署时已自动配置）
sudo systemctl enable monitor-services

# 禁用开机自启
sudo systemctl disable monitor-services
```

## 📊 日志查看

### 系统日志
```bash
# 查看实时日志
sudo journalctl -u monitor-services -f

# 查看最近日志
sudo journalctl -u monitor-services -n 50
```

### 应用日志
```bash
# 查看监控日志
sudo tail -f /opt/monitor/logs/service-monitor.log

# 查看历史日志
sudo less /opt/monitor/logs/service-monitor.log
```

## 🔍 监控验证

### 功能测试
```bash
# 1. 停止一个被监控的服务
sudo systemctl stop your-java-service

# 2. 观察监控日志，应该看到：
# - 检测到服务停止
# - 自动重启服务
# - 发送钉钉通知

# 3. 验证服务已重启
sudo systemctl status your-java-service
```

### 告警测试
```bash
# 多次停止服务测试告警限制
# 第1-5次：正常告警
# 第6次及以后：静默处理
```

## 📁 文件路径说明

### 部署目录结构
```
/opt/monitor/                    # 主目录
├── monitor-services.py          # 监控脚本
├── monitor.conf                 # 配置文件
├── logs/                        # 日志目录
│   └── service-monitor.log      # 监控日志
├── backup/                      # 备份目录
│   └── config-backup/           # 配置备份
└── scripts/                     # 脚本目录
    └── maintenance.sh           # 维护脚本
```

### 系统文件
```
/etc/systemd/system/monitor-services.service    # systemd服务文件
/etc/logrotate.d/monitor-services               # 日志轮转配置
```

## 🛠️ 常见问题

### 1. 服务启动失败
```bash
# 检查配置文件语法
sudo -u monitor python3 /opt/monitor/monitor-services.py /opt/monitor/monitor.conf --help

# 查看详细错误
sudo journalctl -u monitor-services -n 20
```

### 2. 权限问题
```bash
# 修复权限
sudo chown -R monitor:monitor /opt/monitor
sudo chmod 755 /opt/monitor/monitor-services.py
```

### 3. 依赖问题
```bash
# 重新安装依赖
sudo pip3 install requests psutil
```

### 4. 配置修改不生效
```bash
# 重新加载配置
sudo systemctl reload monitor-services

# 或重启服务
sudo systemctl restart monitor-services
```

## 📈 维护操作

### 定期维护
```bash
# 运行维护脚本
sudo /opt/monitor/scripts/maintenance.sh
```

### 配置备份
```bash
# 手动备份配置
sudo cp /opt/monitor/monitor.conf /opt/monitor/backup/config-backup/monitor.conf.$(date +%Y%m%d)
```

### 日志清理
```bash
# 查看日志大小
sudo du -sh /opt/monitor/logs/

# 手动清理（系统会自动清理7天前的日志）
sudo find /opt/monitor/logs/ -name "*.log" -mtime +7 -delete
```

## 🎯 最佳实践

### 1. 监控配置
- 检查间隔建议30-60秒
- 告警次数建议3-5次
- 及时配置钉钉通知

### 2. 服务管理
- 定期检查服务状态
- 监控日志文件大小
- 定期备份配置文件

### 3. 故障处理
- 优先查看系统日志
- 检查配置文件语法
- 验证服务权限设置

---

**部署完成后，监控系统将7×24小时自动运行，确保您的Java服务稳定可靠！**
