#!/usr/bin/env python3
"""
测试监控脚本功能
"""

import sys
import importlib.util

# 动态导入monitor-services.py
spec = importlib.util.spec_from_file_location("monitor_services", "monitor-services.py")
monitor_services = importlib.util.module_from_spec(spec)
spec.loader.exec_module(monitor_services)

def test_config_loading():
    """测试配置加载"""
    print("=== 测试配置加载 ===")
    try:
        monitor = monitor_services.ServiceMonitor('test-config.conf')
        print('✓ 配置加载成功')
        print(f'✓ 监控服务: {monitor.services}')
        print(f'✓ 检查间隔: {monitor.check_interval}秒')
        return monitor
    except Exception as e:
        print(f'✗ 配置加载失败: {e}')
        return None

def test_service_config(monitor):
    """测试服务配置获取"""
    print("\n=== 测试服务配置获取 ===")
    try:
        service_name, jar_path, java_opts, process_keyword = monitor.get_service_config('TEST')
        print(f'✓ 服务名称: {service_name}')
        print(f'✓ JAR路径: {jar_path}')
        print(f'✓ Java选项: {java_opts}')
        print(f'✓ 进程关键字: {process_keyword}')
        return process_keyword
    except Exception as e:
        print(f'✗ 服务配置获取失败: {e}')
        return None

def test_process_check(monitor, process_keyword):
    """测试进程检查"""
    print("\n=== 测试进程检查 ===")
    try:
        is_running = monitor.is_service_running(process_keyword)
        print(f'✓ 进程检查完成，服务运行状态: {is_running}')
        
        # 测试一个肯定存在的进程
        is_running_python = monitor.is_service_running('python')
        print(f'✓ Python进程检查: {is_running_python}')
        
    except Exception as e:
        print(f'✗ 进程检查失败: {e}')

def test_log_cleanup(monitor):
    """测试日志清理功能"""
    print("\n=== 测试日志清理功能 ===")
    try:
        monitor.cleanup_logs()
        print('✓ 日志清理功能测试完成')
    except Exception as e:
        print(f'✗ 日志清理测试失败: {e}')

def main():
    print("开始测试Python版本监控脚本...")
    
    # 测试配置加载
    monitor = test_config_loading()
    if not monitor:
        return
    
    # 测试服务配置获取
    process_keyword = test_service_config(monitor)
    if not process_keyword:
        return
    
    # 测试进程检查
    test_process_check(monitor, process_keyword)
    
    # 测试日志清理
    test_log_cleanup(monitor)
    
    print("\n=== 测试完成 ===")
    print("所有基本功能测试通过！")

if __name__ == "__main__":
    main()
